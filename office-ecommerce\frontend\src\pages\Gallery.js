import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import '../styles/gallery.css';

const Gallery = () => {
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedImage, setSelectedImage] = useState(null);
    const galleryRef = useRef(null);

    const categories = [
        { id: 'all', name: 'All' },
        { id: 'desks', name: '<PERSON><PERSON>' },
        { id: 'chairs', name: 'Chairs' },
        { id: 'storage', name: 'Storage' },
        { id: 'conference', name: 'Conference' }
    ];

    const galleryImages = [
        {
            id: 1,
            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop',
            category: 'desks',
            title: 'Executive Mahogany Desk'
        },
        {
            id: 2,
            src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=400&fit=crop',
            category: 'desks',
            title: 'Modern Glass Desk'
        },
        {
            id: 3,
            src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',
            category: 'chairs',
            title: 'Ergonomic Executive Chair'
        },
        {
            id: 4,
            src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=400&fit=crop',
            category: 'storage',
            title: 'Executive Filing Cabinet'
        },
        {
            id: 5,
            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',
            category: 'conference',
            title: 'Conference Room Setup'
        },
        {
            id: 6,
            src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=400&fit=crop',
            category: 'desks',
            title: 'Standing Desk Setup'
        },
        {
            id: 7,
            src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',
            category: 'chairs',
            title: 'Modern Office Chairs'
        },
        {
            id: 8,
            src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=400&fit=crop',
            category: 'storage',
            title: 'Modern Bookshelf'
        }
    ];

    // Simple filtering
    const filteredImages = galleryImages.filter(img => {
        return selectedCategory === 'all' || img.category === selectedCategory;
    });



    // Intersection Observer for animations
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            },
            { threshold: 0.1 }
        );

        const galleryItems = galleryRef.current?.querySelectorAll('.gallery-item');
        galleryItems?.forEach((item) => observer.observe(item));

        return () => observer.disconnect();
    }, [filteredImages]);

    const openModal = (image) => {
        setSelectedImage(image);
        document.body.style.overflow = 'hidden';
    };

    const closeModal = () => {
        setSelectedImage(null);
        document.body.style.overflow = 'unset';
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Escape') closeModal();
    };

    useEffect(() => {
        document.addEventListener('keydown', handleKeyPress);
        return () => document.removeEventListener('keydown', handleKeyPress);
    }, []);

    return (
        <div className="gallery-page">
            <div className="container">
                {/* Simple Header */}
                <div className="gallery-header">
                    <div className="breadcrumb">
                        <Link to="/">Home</Link> / <span>Gallery</span>
                    </div>
                    <h1 className="gallery-title">Gallery</h1>
                    <p className="gallery-subtitle">Explore our office furniture collection</p>
                    <div className="tagline">
                        <p>Transform your workspace with premium office furniture designed for productivity and style</p>
                    </div>
                </div>
                {/* Simple Controls */}
                <div className="gallery-controls">
                    <div className="filter-tabs">
                        {categories.map(category => (
                            <button
                                key={category.id}
                                className={`filter-tab ${selectedCategory === category.id ? 'active' : ''}`}
                                onClick={() => setSelectedCategory(category.id)}
                            >
                                {category.name}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Simple Gallery Grid */}
                <div className="gallery-grid" ref={galleryRef}>
                    {filteredImages.length > 0 ? (
                        filteredImages.map((image) => (
                            <div
                                key={image.id}
                                className="gallery-item"
                                onClick={() => openModal(image)}
                            >
                                <img
                                    src={image.src}
                                    alt={image.title}
                                    loading="lazy"
                                />
                                <div className="image-overlay">
                                    <h3>{image.title}</h3>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="no-results">
                            <p>No images found</p>
                        </div>
                    )}
                </div>

            </div>

            {/* Simple Modal */}
            {selectedImage && (
                <div className="modal-overlay" onClick={closeModal}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <button className="modal-close" onClick={closeModal}>×</button>
                        <img src={selectedImage.src} alt={selectedImage.title} />
                        <div className="modal-info">
                            <h3>{selectedImage.title}</h3>
                            <Link
                                to="/products"
                                className="btn btn-primary"
                                onClick={closeModal}
                            >
                                View Products
                            </Link>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Gallery;
