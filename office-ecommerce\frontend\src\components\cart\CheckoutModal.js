import React from 'react';
import { Link } from 'react-router-dom';

const CheckoutModal = ({ isOpen, onClose, product, quantity = 1 }) => {
    if (!isOpen || !product) return null;

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    const displayPrice = product.discountPrice || product.price;
    const totalPrice = displayPrice * quantity;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="checkout-modal" onClick={(e) => e.stopPropagation()}>
                <button className="modal-close" onClick={onClose}>×</button>
                
                <div className="checkout-modal-content">
                    {/* Success Icon */}
                    <div className="success-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="10" fill="#F0B21B"/>
                            <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </div>

                    {/* Success Message */}
                    <h3 className="success-title">Item Added to Cart!</h3>

                    {/* Product Info */}
                    <div className="added-product-info">
                        <div className="product-image">
                            <img 
                                src={product.images && product.images[0] ? product.images[0] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop'} 
                                alt={product.name}
                                onError={(e) => {
                                    e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop';
                                }}
                            />
                        </div>
                        <div className="product-details">
                            <h4>{product.name}</h4>
                            <p className="product-price">{formatPrice(displayPrice)}</p>
                            {quantity > 1 && (
                                <p className="quantity-info">Quantity: {quantity}</p>
                            )}
                            <p className="total-price">Total: {formatPrice(totalPrice)}</p>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="checkout-modal-actions">
                        <button 
                            className="btn btn-secondary continue-shopping"
                            onClick={onClose}
                        >
                            Continue Shopping
                        </button>
                        <Link 
                            to="/cart" 
                            className="btn btn-primary view-cart"
                            onClick={onClose}
                        >
                            View Cart
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CheckoutModal;
