* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Design Excellence Brand Colors */
:root {
    --primary-color: #F0B21B;
    --primary-dark: #e6a632;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --background-light: #f8f9fa;
    --white: #ffffff;
    --border-color: #e9ecef;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Currency and Language Selector */
.currency-language-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.selector-dropdown {
    position: relative;
}

.selector-button {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    transition: background-color 0.3s;
}

.selector-button:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--text-dark);
}

.dropdown-arrow {
    transition: transform 0.3s;
}

.dropdown-arrow.open {
    transform: rotate(180deg);
}

.selector-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 180px;
    z-index: 1001;
    overflow: hidden;
    margin-top: 0.25rem;
}

.dropdown-item {
    width: 100%;
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    text-align: left;
    cursor: pointer;
    color: var(--text-dark);
    font-size: 0.85rem;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.dropdown-item:hover {
    background: var(--background-light);
}

.dropdown-item.selected {
    background: var(--primary-color);
    color: var(--white);
}

.currency-code {
    font-weight: 600;
}

.currency-name {
    color: var(--text-light);
}

.dropdown-item.selected .currency-name {
    color: rgba(255, 255, 255, 0.8);
}

/* Header Styles */
.header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 0;
    text-align: center;
    font-size: 14px;
}

.header-main {
    padding: 15px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Generic logo styles - avoid conflicts with header logo */
.logo-generic {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: var(--text-dark);
    text-decoration: none;
}

.logo-icon {
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 12px;
    margin-right: 12px;
    border-radius: 4px;
}

.search-bar {
    flex: 1;
    max-width: 400px;
    margin: 0 40px;
    position: relative;
}

.search-bar input {
    width: 100%;
    padding: 12px 20px;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s;
}

.search-bar input:focus {
    border-color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    color: var(--text-light);
}

.action-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-dark);
    transition: color 0.3s;
}

.action-btn:hover {
    color: var(--primary-color);
}

/* Navigation */
.navigation {
    background: #F0B21B;
    padding: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 15px 25px;
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-dark);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--secondary-color) 0%,#FFDB58 100%);
    color: var(--white);
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-button {
    background: var(--primary-color);
    color: var(--white);
    padding: 15px 40px;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.hero-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    pointer-events: none;
}

.nav-arrow {
    background: rgba(255,255,255,0.2);
    border: none;
    color: var(--white);
    font-size: 24px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
    pointer-events: all;
}

.nav-arrow:hover {
    background: rgba(255,255,255,0.3);
}

.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    cursor: pointer;
    transition: background-color 0.3s;
}

.indicator.active {
    background: var(--primary-color);
}

/* Sections */
.section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s;
}

.view-all:hover {
    color: var(--primary-dark);
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.category-card {
    background: var(--white);
    padding: 40px 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.category-card h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.category-card p {
    color: var(--text-light);
    font-size: 14px;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.product-card {
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.product-badge.featured {
    background: var(--accent-color);
}

.product-info {
    padding: 25px;
}

.product-title {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 600;
}

.product-category {
    color: var(--text-light);
    font-size: 14px;
    margin-bottom: 15px;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.price-current {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-dark);
}

.price-original {
    font-size: 1.1rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: transparent;
    color: var(--text-dark);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Contact Section */
.contact-section {
    background: var(--background-light);
    padding: 80px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.2rem;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.contact-info p {
    color: var(--text-light);
    margin-bottom: 40px;
    font-size: 1.1rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 15px;
}

.method-icon {
    background: var(--primary-color);
    color: var(--white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.contact-method h4 {
    color: var(--text-dark);
    margin-bottom: 5px;
}

.contact-method p {
    color: var(--text-light);
    margin: 0;
}

.contact-form {
    background: var(--white);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 25px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.submit-button {
    background: var(--primary-color);
    color: var(--white);
    padding: 15px 40px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
    width: 100%;
}

.submit-button:hover {
    background: var(--primary-dark);
}

/* Footer */
.footer {
    background: var(--background-light);
    color: var(--text-dark);
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-bottom: 40px;
}

/* Left Side - Contact Information */
.footer-left {
    max-width: 500px;
}

.footer-left h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

.footer-left > p {
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.method-icon {
    background: #F0B21B;
    padding: 0.75rem;
    border-radius: 8px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-method h4 {
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.contact-method p {
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

.company-tagline {
    background: #F0B21B;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-top: 2rem;
}

.company-tagline p {
    color: var(--white);
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Right Side - Contact Form */
.footer-right {
    background: var(--white);
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.contact-form-header {
    background: #F0B21B;
    padding: 1rem 1.5rem;
    text-align: center;
}

.contact-form-badge {
    color: var(--white);
    font-weight: 600;
    font-size: 1rem;
}

.footer-contact-form {
    padding: 2rem 1.5rem;
}

.footer-contact-form .form-group {
    margin-bottom: 1.5rem;
}

.footer-contact-form input,
.footer-contact-form textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s;
    background: #f8f9fa;
}

.footer-contact-form input:focus,
.footer-contact-form textarea:focus {
    outline: none;
    border-color: #F0B21B;
    background: var(--white);
}

.footer-contact-form input::placeholder,
.footer-contact-form textarea::placeholder {
    color: #999;
}

.send-message-btn {
    width: 100%;
    background: #F0B21B;
    color: var(--white);
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
}

.send-message-btn:hover {
    background: #e6a632;
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 30px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    text-align: center;
}

.footer-section h4 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s;
    font-size: 0.9rem;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

/* Social Links */
.social-links {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.social-link {
    background: #F0B21B;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-link:hover {
    background: #e6a632;
    transform: translateY(-2px);
}

/* Loading Spinner */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: var(--text-light);
}

.spinner {
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin-right: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}



/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
    }

    .search-bar {
        max-width: 100%;
        margin: 0;
    }

    .nav-menu {
        flex-direction: column;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-left {
        max-width: 100%;
        text-align: center;
    }

    .footer-left h2 {
        font-size: 2rem;
    }

    .contact-methods {
        align-items: center;
    }

    .contact-method {
        justify-content: center;
        text-align: left;
        max-width: 300px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 60px 0;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .section {
        padding: 60px 0;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .contact-form {
        padding: 25px;
    }
}