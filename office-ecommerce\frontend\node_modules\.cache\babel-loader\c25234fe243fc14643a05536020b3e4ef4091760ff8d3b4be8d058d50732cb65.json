{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\ProductDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { getProductById } from '../services/products';\nimport { useCart } from '../contexts/CartContext';\nimport CheckoutModal from '../components/cart/CheckoutModal';\nimport Advanced3DConfigurator from '../components/3d/3DConfigurator';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    addToCart\n  } = useCart();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [show3DConfigurator, setShow3DConfigurator] = useState(false);\n  const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n  const [customization, setCustomization] = useState({\n    color: '#6B7280',\n    material: 'wood',\n    dimensions: {\n      width: 120,\n      height: 75,\n      depth: 60\n    }\n  });\n  const [quantity, setQuantity] = useState(1);\n  useEffect(() => {\n    loadProduct();\n  }, [id]);\n  const loadProduct = async () => {\n    try {\n      const response = await getProductById(id);\n      setProduct(response.product);\n    } catch (error) {\n      console.error('Error loading product:', error);\n      setError('Failed to load product details');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatPrice = price => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  // Check if product supports advanced 3D configuration\n  const supportsAdvanced3D = product => {\n    if (!product) return false;\n    const productType = product.name.toLowerCase();\n    return productType.includes('table') || productType.includes('desk') || productType.includes('chair') || productType.includes('cabinet') || productType.includes('storage') || productType.includes('workstation') || productType.includes('shelf');\n  };\n\n  // Handle adding to cart with customization\n  const handleAddToCart = () => {\n    if (!product) return;\n    try {\n      addToCart(product, quantity, customization);\n      setShowCheckoutModal(true);\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      alert('Failed to add item to cart. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-detail-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: \"Loading product details...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-detail-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [error || 'Product not found', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-primary\",\n            children: \"Back to Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 13\n    }, this);\n  }\n  const {\n    name,\n    description,\n    price,\n    discountPrice,\n    images,\n    categoryName,\n    specifications\n  } = product;\n  const displayPrice = discountPrice || price;\n  const hasDiscount = discountPrice && discountPrice < price;\n\n  // Check if this product supports advanced 3D configuration\n  const isConfigurable3D = supportsAdvanced3D(product);\n\n  // If 3D configurator is enabled and it's a configurable product, show the configurator\n  if (show3DConfigurator && isConfigurable3D) {\n    return /*#__PURE__*/_jsxDEV(Advanced3DConfigurator, {\n      product: product,\n      onBack: () => setShow3DConfigurator(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-detail-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"breadcrumb\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this), \" /\", /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/products\",\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this), \" /\", /*#__PURE__*/_jsxDEV(\"span\", {\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-detail\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-media\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"main-media\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"main-image\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: images && images[selectedImage] ? images[selectedImage] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600',\n                alt: name,\n                onError: e => {\n                  e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 33\n              }, this), isConfigurable3D && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-overlay\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"configurator-btn\",\n                  onClick: () => setShow3DConfigurator(true),\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"16\",\n                    height: \"16\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                      fill: \"currentColor\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 17L12 22L22 17\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 12L12 17L22 12\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 45\n                  }, this), \"3D Configurator\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"media-thumbnails\",\n            children: images && images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-thumbnails\",\n              children: images.map((image, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n                src: image,\n                alt: `${name} ${index + 1}`,\n                className: selectedImage === index ? 'active' : '',\n                onClick: () => setSelectedImage(index),\n                onError: e => {\n                  e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-category\",\n            children: categoryName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-pricing\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-price\",\n              children: formatPrice(displayPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 29\n            }, this), hasDiscount && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"original-price\",\n                children: formatPrice(price)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"discount-badge\",\n                children: [Math.round((price - discountPrice) / price * 100), \"% OFF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-description\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 25\n          }, this), specifications && Object.keys(specifications).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-specifications\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Specifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: Object.entries(specifications).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()), \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 45\n                }, this), \" \", value]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-customization\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Product Options\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"customization-options\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Quantity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"quantity-control\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setQuantity(Math.max(1, quantity - 1)),\n                    className: \"quantity-btn\",\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"quantity-value\",\n                    children: quantity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setQuantity(quantity + 1),\n                    className: \"quantity-btn\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 33\n              }, this), isConfigurable3D && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Advanced Customization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"customization-note\",\n                  children: \"Use the 3D Configurator to customize colors, materials, dimensions, and more advanced options.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary btn-large\",\n              onClick: handleAddToCart,\n              children: [\"Add to Cart - \", formatPrice((discountPrice || price) * quantity)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 29\n            }, this), isConfigurable3D && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-3d-configurator btn-large\",\n              onClick: () => setShow3DConfigurator(true),\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 17L12 22L22 17\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 12L12 17L22 12\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 37\n              }, this), \"3D Configurator\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"related-products\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Related Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"related-products-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Hotel Bed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$2,299\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Conference Table\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$2,499\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\",\n                alt: \"Related Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Reception Desk\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"$1,899\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(CheckoutModal, {\n      isOpen: showCheckoutModal,\n      onClose: () => setShowCheckoutModal(false),\n      product: product,\n      quantity: quantity\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 9\n  }, this);\n};\n_s(ProductDetail, \"CUI6LraouZxTbceAwWcnm3lxOYs=\", false, function () {\n  return [useParams, useCart];\n});\n_c = ProductDetail;\nexport default ProductDetail;\nvar _c;\n$RefreshReg$(_c, \"ProductDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "getProductById", "useCart", "CheckoutModal", "Advanced3DConfigurator", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductDetail", "_s", "id", "addToCart", "product", "setProduct", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "show3DConfigurator", "setShow3DConfigurator", "showCheckoutModal", "setShowCheckoutModal", "customization", "setCustomization", "color", "material", "dimensions", "width", "height", "depth", "quantity", "setQuantity", "loadProduct", "response", "console", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "supportsAdvanced3D", "productType", "name", "toLowerCase", "includes", "handleAddToCart", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "description", "discountPrice", "images", "categoryName", "specifications", "displayPrice", "hasDiscount", "isConfigurable3D", "onBack", "src", "alt", "onError", "e", "target", "onClick", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "length", "map", "image", "index", "Math", "round", "Object", "keys", "entries", "key", "value", "replace", "str", "toUpperCase", "max", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/ProductDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport { getProductById } from '../services/products';\nimport { useCart } from '../contexts/CartContext';\nimport CheckoutModal from '../components/cart/CheckoutModal';\nimport Advanced3DConfigurator from '../components/3d/3DConfigurator';\n\nconst ProductDetail = () => {\n    const { id } = useParams();\n    const { addToCart } = useCart();\n    const [product, setProduct] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [selectedImage, setSelectedImage] = useState(0);\n    const [show3DConfigurator, setShow3DConfigurator] = useState(false);\n    const [showCheckoutModal, setShowCheckoutModal] = useState(false);\n    const [customization, setCustomization] = useState({\n        color: '#6B7280',\n        material: 'wood',\n        dimensions: { width: 120, height: 75, depth: 60 }\n    });\n    const [quantity, setQuantity] = useState(1);\n\n    useEffect(() => {\n        loadProduct();\n    }, [id]);\n\n    const loadProduct = async () => {\n        try {\n            const response = await getProductById(id);\n            setProduct(response.product);\n        } catch (error) {\n            console.error('Error loading product:', error);\n            setError('Failed to load product details');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const formatPrice = (price) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(price);\n    };\n\n    // Check if product supports advanced 3D configuration\n    const supportsAdvanced3D = (product) => {\n        if (!product) return false;\n        const productType = product.name.toLowerCase();\n        return productType.includes('table') ||\n               productType.includes('desk') ||\n               productType.includes('chair') ||\n               productType.includes('cabinet') ||\n               productType.includes('storage') ||\n               productType.includes('workstation') ||\n               productType.includes('shelf');\n    };\n\n    // Handle adding to cart with customization\n    const handleAddToCart = () => {\n        if (!product) return;\n\n        try {\n            addToCart(product, quantity, customization);\n            setShowCheckoutModal(true);\n        } catch (error) {\n            console.error('Error adding to cart:', error);\n            alert('Failed to add item to cart. Please try again.');\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"product-detail-page\">\n                <div className=\"container\">\n                    <div className=\"loading\">Loading product details...</div>\n                </div>\n            </div>\n        );\n    }\n\n    if (error || !product) {\n        return (\n            <div className=\"product-detail-page\">\n                <div className=\"container\">\n                    <div className=\"error-message\">\n                        {error || 'Product not found'}\n                        <Link to=\"/products\" className=\"btn btn-primary\">\n                            Back to Products\n                        </Link>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    const {\n        name,\n        description,\n        price,\n        discountPrice,\n        images,\n        categoryName,\n        specifications\n    } = product;\n\n    const displayPrice = discountPrice || price;\n    const hasDiscount = discountPrice && discountPrice < price;\n\n    // Check if this product supports advanced 3D configuration\n    const isConfigurable3D = supportsAdvanced3D(product);\n\n    // If 3D configurator is enabled and it's a configurable product, show the configurator\n    if (show3DConfigurator && isConfigurable3D) {\n        return <Advanced3DConfigurator product={product} onBack={() => setShow3DConfigurator(false)} />;\n    }\n\n    return (\n        <div className=\"product-detail-page\">\n            <div className=\"container\">\n                <div className=\"breadcrumb\">\n                    <Link to=\"/\">Home</Link> /\n                    <Link to=\"/products\">Products</Link> /\n                    <span>{name}</span>\n                </div>\n\n                <div className=\"product-detail\">\n                    <div className=\"product-media\">\n                        {/* Main Media Display */}\n                        <div className=\"main-media\">\n                            <div className=\"main-image\">\n                                <img\n                                    src={images && images[selectedImage] ? images[selectedImage] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600'}\n                                    alt={name}\n                                    onError={(e) => {\n                                        e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600';\n                                    }}\n                                />\n                                {isConfigurable3D && (\n                                    <div className=\"image-overlay\">\n                                        <button\n                                            className=\"configurator-btn\"\n                                            onClick={() => setShow3DConfigurator(true)}\n                                        >\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                                <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"currentColor\"/>\n                                                <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                                <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                            </svg>\n                                            3D Configurator\n                                        </button>\n                                    </div>\n                                )}\n                            </div>\n                        </div>\n\n                        {/* Thumbnails */}\n                        <div className=\"media-thumbnails\">\n                            {images && images.length > 1 && (\n                                <div className=\"image-thumbnails\">\n                                    {images.map((image, index) => (\n                                        <img\n                                            key={index}\n                                            src={image}\n                                            alt={`${name} ${index + 1}`}\n                                            className={selectedImage === index ? 'active' : ''}\n                                            onClick={() => setSelectedImage(index)}\n                                            onError={(e) => {\n                                                e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';\n                                            }}\n                                        />\n                                    ))}\n                                </div>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"product-info\">\n                        <div className=\"product-category\">{categoryName}</div>\n                        <h1>{name}</h1>\n\n                        <div className=\"product-pricing\">\n                            <span className=\"current-price\">{formatPrice(displayPrice)}</span>\n                            {hasDiscount && (\n                                <>\n                                    <span className=\"original-price\">{formatPrice(price)}</span>\n                                    <span className=\"discount-badge\">\n                                        {Math.round(((price - discountPrice) / price) * 100)}% OFF\n                                    </span>\n                                </>\n                            )}\n                        </div>\n\n                        <div className=\"product-description\">\n                            <p>{description}</p>\n                        </div>\n\n                        {specifications && Object.keys(specifications).length > 0 && (\n                            <div className=\"product-specifications\">\n                                <h3>Specifications</h3>\n                                <ul>\n                                    {Object.entries(specifications).map(([key, value]) => (\n                                        <li key={key}>\n                                            <strong>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong> {value}\n                                        </li>\n                                    ))}\n                                </ul>\n                            </div>\n                        )}\n\n                        {/* Basic Customization Options */}\n                        <div className=\"product-customization\">\n                            <h3>Product Options</h3>\n\n                            <div className=\"customization-options\">\n                                <div className=\"option-group\">\n                                    <label>Quantity</label>\n                                    <div className=\"quantity-control\">\n                                        <button\n                                            onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                                            className=\"quantity-btn\"\n                                        >\n                                            -\n                                        </button>\n                                        <span className=\"quantity-value\">{quantity}</span>\n                                        <button\n                                            onClick={() => setQuantity(quantity + 1)}\n                                            className=\"quantity-btn\"\n                                        >\n                                            +\n                                        </button>\n                                    </div>\n                                </div>\n\n                                {isConfigurable3D && (\n                                    <div className=\"option-group\">\n                                        <label>Advanced Customization</label>\n                                        <p className=\"customization-note\">\n                                            Use the 3D Configurator to customize colors, materials, dimensions, and more advanced options.\n                                        </p>\n                                    </div>\n                                )}\n                            </div>\n                        </div>\n\n                        <div className=\"product-actions\">\n                            <button\n                                className=\"btn btn-primary btn-large\"\n                                onClick={handleAddToCart}\n                            >\n                                Add to Cart - {formatPrice((discountPrice || price) * quantity)}\n                            </button>\n                            {isConfigurable3D && (\n                                <button\n                                    className=\"btn btn-3d-configurator btn-large\"\n                                    onClick={() => setShow3DConfigurator(true)}\n                                >\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                        <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"currentColor\"/>\n                                        <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                        <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    3D Configurator\n                                </button>\n                            )}\n                        </div>\n                    </div>\n                </div>\n\n                {/* Related Products Section */}\n                <div className=\"related-products\">\n                    <h2>Related Products</h2>\n                    <div className=\"related-products-grid\">\n                        {/* Placeholder for related products */}\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Hotel Bed</h4>\n                                <p>$2,299</p>\n                            </div>\n                        </div>\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Conference Table</h4>\n                                <p>$2,499</p>\n                            </div>\n                        </div>\n                        <div className=\"related-product-card\">\n                            <div className=\"related-product-image\">\n                                <img src=\"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300\" alt=\"Related Product\" />\n                            </div>\n                            <div className=\"related-product-info\">\n                                <h4>Reception Desk</h4>\n                                <p>$1,899</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Checkout Modal */}\n            <CheckoutModal\n                isOpen={showCheckoutModal}\n                onClose={() => setShowCheckoutModal(false)}\n                product={product}\n                quantity={quantity}\n            />\n        </div>\n    );\n};\n\nexport default ProductDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,sBAAsB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEa;EAAU,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACwB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC;IAC/C8B,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG;EACpD,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACZqC,WAAW,CAAC,CAAC;EACjB,CAAC,EAAE,CAACxB,EAAE,CAAC,CAAC;EAER,MAAMwB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMnC,cAAc,CAACU,EAAE,CAAC;MACzCG,UAAU,CAACsB,QAAQ,CAACvB,OAAO,CAAC;IAChC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZoB,OAAO,CAACpB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,gCAAgC,CAAC;IAC9C,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsB,WAAW,GAAIC,KAAK,IAAK;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAIhC,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMiC,WAAW,GAAGjC,OAAO,CAACkC,IAAI,CAACC,WAAW,CAAC,CAAC;IAC9C,OAAOF,WAAW,CAACG,QAAQ,CAAC,OAAO,CAAC,IAC7BH,WAAW,CAACG,QAAQ,CAAC,MAAM,CAAC,IAC5BH,WAAW,CAACG,QAAQ,CAAC,OAAO,CAAC,IAC7BH,WAAW,CAACG,QAAQ,CAAC,SAAS,CAAC,IAC/BH,WAAW,CAACG,QAAQ,CAAC,SAAS,CAAC,IAC/BH,WAAW,CAACG,QAAQ,CAAC,aAAa,CAAC,IACnCH,WAAW,CAACG,QAAQ,CAAC,OAAO,CAAC;EACxC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACrC,OAAO,EAAE;IAEd,IAAI;MACAD,SAAS,CAACC,OAAO,EAAEoB,QAAQ,EAAER,aAAa,CAAC;MAC3CD,oBAAoB,CAAC,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZoB,OAAO,CAACpB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CkC,KAAK,CAAC,+CAA+C,CAAC;IAC1D;EACJ,CAAC;EAED,IAAIpC,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK8C,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChC/C,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtB/C,OAAA;UAAK8C,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIxC,KAAK,IAAI,CAACJ,OAAO,EAAE;IACnB,oBACIP,OAAA;MAAK8C,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChC/C,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtB/C,OAAA;UAAK8C,SAAS,EAAC,eAAe;UAAAC,QAAA,GACzBpC,KAAK,IAAI,mBAAmB,eAC7BX,OAAA,CAACN,IAAI;YAAC0D,EAAE,EAAC,WAAW;YAACN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,MAAM;IACFV,IAAI;IACJY,WAAW;IACXpB,KAAK;IACLqB,aAAa;IACbC,MAAM;IACNC,YAAY;IACZC;EACJ,CAAC,GAAGlD,OAAO;EAEX,MAAMmD,YAAY,GAAGJ,aAAa,IAAIrB,KAAK;EAC3C,MAAM0B,WAAW,GAAGL,aAAa,IAAIA,aAAa,GAAGrB,KAAK;;EAE1D;EACA,MAAM2B,gBAAgB,GAAGrB,kBAAkB,CAAChC,OAAO,CAAC;;EAEpD;EACA,IAAIQ,kBAAkB,IAAI6C,gBAAgB,EAAE;IACxC,oBAAO5D,OAAA,CAACF,sBAAsB;MAACS,OAAO,EAAEA,OAAQ;MAACsD,MAAM,EAAEA,CAAA,KAAM7C,qBAAqB,CAAC,KAAK;IAAE;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnG;EAEA,oBACInD,OAAA;IAAK8C,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChC/C,OAAA;MAAK8C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtB/C,OAAA;QAAK8C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB/C,OAAA,CAACN,IAAI;UAAC0D,EAAE,EAAC,GAAG;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MACxB,eAAAnD,OAAA,CAACN,IAAI;UAAC0D,EAAE,EAAC,WAAW;UAAAL,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MACpC,eAAAnD,OAAA;UAAA+C,QAAA,EAAON;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3B/C,OAAA;UAAK8C,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAE1B/C,OAAA;YAAK8C,SAAS,EAAC,YAAY;YAAAC,QAAA,eACvB/C,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB/C,OAAA;gBACI8D,GAAG,EAAEP,MAAM,IAAIA,MAAM,CAAC1C,aAAa,CAAC,GAAG0C,MAAM,CAAC1C,aAAa,CAAC,GAAG,oEAAqE;gBACpIkD,GAAG,EAAEtB,IAAK;gBACVuB,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,oEAAoE;gBACvF;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACDS,gBAAgB,iBACb5D,OAAA;gBAAK8C,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC1B/C,OAAA;kBACI8C,SAAS,EAAC,kBAAkB;kBAC5BqB,OAAO,EAAEA,CAAA,KAAMnD,qBAAqB,CAAC,IAAI,CAAE;kBAAA+B,QAAA,gBAE3C/C,OAAA;oBAAKwB,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC2C,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,KAAK,EAAC,4BAA4B;oBAAAvB,QAAA,gBAC1F/C,OAAA;sBAAMuE,CAAC,EAAC,4BAA4B;sBAACF,IAAI,EAAC;oBAAc;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAC1DnD,OAAA;sBAAMuE,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHnD,OAAA;sBAAMuE,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/G,CAAC,mBAEV;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNnD,OAAA;YAAK8C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC5BQ,MAAM,IAAIA,MAAM,CAACqB,MAAM,GAAG,CAAC,iBACxB5E,OAAA;cAAK8C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC5BQ,MAAM,CAACsB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACrB/E,OAAA;gBAEI8D,GAAG,EAAEgB,KAAM;gBACXf,GAAG,EAAE,GAAGtB,IAAI,IAAIsC,KAAK,GAAG,CAAC,EAAG;gBAC5BjC,SAAS,EAAEjC,aAAa,KAAKkE,KAAK,GAAG,QAAQ,GAAG,EAAG;gBACnDZ,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACiE,KAAK,CAAE;gBACvCf,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,oEAAoE;gBACvF;cAAE,GAPGiB,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQb,CACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzB/C,OAAA;YAAK8C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAES;UAAY;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDnD,OAAA;YAAA+C,QAAA,EAAKN;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAEfnD,OAAA;YAAK8C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEf,WAAW,CAAC0B,YAAY;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACjEQ,WAAW,iBACR3D,OAAA,CAAAE,SAAA;cAAA6C,QAAA,gBACI/C,OAAA;gBAAM8C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEf,WAAW,CAACC,KAAK;cAAC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5DnD,OAAA;gBAAM8C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC3BiC,IAAI,CAACC,KAAK,CAAE,CAAChD,KAAK,GAAGqB,aAAa,IAAIrB,KAAK,GAAI,GAAG,CAAC,EAAC,OACzD;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACT,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENnD,OAAA;YAAK8C,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAChC/C,OAAA;cAAA+C,QAAA,EAAIM;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EAELM,cAAc,IAAIyB,MAAM,CAACC,IAAI,CAAC1B,cAAc,CAAC,CAACmB,MAAM,GAAG,CAAC,iBACrD5E,OAAA;YAAK8C,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnC/C,OAAA;cAAA+C,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBnD,OAAA;cAAA+C,QAAA,EACKmC,MAAM,CAACE,OAAO,CAAC3B,cAAc,CAAC,CAACoB,GAAG,CAAC,CAAC,CAACQ,GAAG,EAAEC,KAAK,CAAC,kBAC7CtF,OAAA;gBAAA+C,QAAA,gBACI/C,OAAA;kBAAA+C,QAAA,GAASsC,GAAG,CAACE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,IAAI,EAAEC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACmC,KAAK;cAAA,GAD5FD,GAAG;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR,eAGDnD,OAAA;YAAK8C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClC/C,OAAA;cAAA+C,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAExBnD,OAAA;cAAK8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAClC/C,OAAA;gBAAK8C,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAA+C,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBnD,OAAA;kBAAK8C,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC7B/C,OAAA;oBACImE,OAAO,EAAEA,CAAA,KAAMvC,WAAW,CAACoD,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE/D,QAAQ,GAAG,CAAC,CAAC,CAAE;oBACtDmB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnD,OAAA;oBAAM8C,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAEpB;kBAAQ;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClDnD,OAAA;oBACImE,OAAO,EAAEA,CAAA,KAAMvC,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;oBACzCmB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAELS,gBAAgB,iBACb5D,OAAA;gBAAK8C,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAA+C,QAAA,EAAO;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrCnD,OAAA;kBAAG8C,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENnD,OAAA;YAAK8C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B/C,OAAA;cACI8C,SAAS,EAAC,2BAA2B;cACrCqB,OAAO,EAAEvB,eAAgB;cAAAG,QAAA,GAC5B,gBACiB,EAACf,WAAW,CAAC,CAACsB,aAAa,IAAIrB,KAAK,IAAIN,QAAQ,CAAC;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,EACRS,gBAAgB,iBACb5D,OAAA;cACI8C,SAAS,EAAC,mCAAmC;cAC7CqB,OAAO,EAAEA,CAAA,KAAMnD,qBAAqB,CAAC,IAAI,CAAE;cAAA+B,QAAA,gBAE3C/C,OAAA;gBAAKwB,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAAC2C,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAvB,QAAA,gBAC1F/C,OAAA;kBAAMuE,CAAC,EAAC,4BAA4B;kBAACF,IAAI,EAAC;gBAAc;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC1DnD,OAAA;kBAAMuE,CAAC,EAAC,mBAAmB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChHnD,OAAA;kBAAMuE,CAAC,EAAC,mBAAmB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,mBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNnD,OAAA;QAAK8C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7B/C,OAAA;UAAA+C,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBnD,OAAA;UAAK8C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAElC/C,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC/C,OAAA;cAAK8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClC/C,OAAA;gBAAK8D,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjC/C,OAAA;gBAAA+C,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBnD,OAAA;gBAAA+C,QAAA,EAAG;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC/C,OAAA;cAAK8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClC/C,OAAA;gBAAK8D,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjC/C,OAAA;gBAAA+C,QAAA,EAAI;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBnD,OAAA;gBAAA+C,QAAA,EAAG;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC/C,OAAA;cAAK8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClC/C,OAAA;gBAAK8D,GAAG,EAAC,oEAAoE;gBAACC,GAAG,EAAC;cAAiB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjC/C,OAAA;gBAAA+C,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBnD,OAAA;gBAAA+C,QAAA,EAAG;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNnD,OAAA,CAACH,aAAa;MACV8F,MAAM,EAAE1E,iBAAkB;MAC1B2E,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAAC,KAAK,CAAE;MAC3CX,OAAO,EAAEA,OAAQ;MACjBoB,QAAQ,EAAEA;IAAS;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC/C,EAAA,CApTID,aAAa;EAAA,QACAV,SAAS,EACFG,OAAO;AAAA;AAAAiG,EAAA,GAF3B1F,aAAa;AAsTnB,eAAeA,aAAa;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}