import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';

const CartItem = ({ item }) => {
    const { updateQuantity, removeFromCart } = useCart();

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    const handleQuantityChange = (newQuantity) => {
        if (newQuantity < 1) {
            removeFromCart(item.id);
        } else {
            updateQuantity(item.id, newQuantity);
        }
    };

    const getCustomizationDisplay = () => {
        const { customization } = item;
        if (!customization || Object.keys(customization).length === 0) {
            return null;
        }

        const customizations = [];
        if (customization.color) {
            customizations.push(`Color: ${customization.color}`);
        }
        if (customization.material) {
            customizations.push(`Material: ${customization.material}`);
        }
        if (customization.dimensions) {
            const { width, height, depth } = customization.dimensions;
            customizations.push(`Size: ${width}×${height}×${depth}cm`);
        }

        return customizations.length > 0 ? customizations.join(', ') : null;
    };

    const customizationText = getCustomizationDisplay();

    return (
        <div className="cart-item">
            <div className="cart-item-image">
                <Link to={`/products/${item.product.id}`}>
                    <img 
                        src={item.product.images?.[0] || 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150'} 
                        alt={item.product.name}
                        onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';
                        }}
                    />
                </Link>
            </div>

            <div className="cart-item-details">
                <div className="cart-item-info">
                    <Link 
                        to={`/products/${item.product.id}`}
                        className="cart-item-name"
                    >
                        {item.product.name}
                    </Link>
                    
                    {customizationText && (
                        <div className="cart-item-customization">
                            {customizationText}
                        </div>
                    )}

                    <div className="cart-item-price">
                        {formatPrice(item.price)}
                    </div>
                </div>

                <div className="cart-item-controls">
                    <div className="quantity-controls">
                        <button 
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.quantity - 1)}
                            aria-label="Decrease quantity"
                        >
                            −
                        </button>
                        <span className="quantity-display">
                            {item.quantity}
                        </span>
                        <button 
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.quantity + 1)}
                            aria-label="Increase quantity"
                        >
                            +
                        </button>
                    </div>

                    <button 
                        className="remove-btn"
                        onClick={() => removeFromCart(item.id)}
                        aria-label="Remove item from cart"
                        title="Remove from cart"
                    >
                        🗑️
                    </button>
                </div>

                <div className="cart-item-total">
                    {formatPrice(item.price * item.quantity)}
                </div>
            </div>
        </div>
    );
};

export default CartItem;
