import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';

const SearchSuggestions = ({ 
    suggestions, 
    selectedIndex, 
    onSelect, 
    onClose, 
    formatPrice, 
    isLoading 
}) => {
    const { t } = useLanguage();

    if (isLoading) {
        return (
            <div className="search-suggestions">
                <div className="suggestions-loading">
                    <div className="loading-spinner"></div>
                    <span>{t('loading')}...</span>
                </div>
            </div>
        );
    }

    if (suggestions.length === 0) {
        return (
            <div className="search-suggestions">
                <div className="no-suggestions">
                    <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </svg>
                    <span>No products found</span>
                </div>
            </div>
        );
    }

    return (
        <div className="search-suggestions">
            <div className="suggestions-header">
                <span className="suggestions-count">
                    {suggestions.length} {suggestions.length === 1 ? 'product' : 'products'} found
                </span>
                <button 
                    className="suggestions-close"
                    onClick={onClose}
                    aria-label="Close suggestions"
                >
                    <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M18 6L6 18M6 6L18 18"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </svg>
                </button>
            </div>
            
            <div className="suggestions-list">
                {suggestions.map((product, index) => (
                    <div
                        key={product.id}
                        className={`suggestion-item ${index === selectedIndex ? 'selected' : ''}`}
                        onClick={() => onSelect(product)}
                        onMouseEnter={() => {
                            // Optional: Update selected index on hover
                        }}
                    >
                        <div className="suggestion-image">
                            <img
                                src={product.images?.[0] || '/placeholder-image.jpg'}
                                alt={product.name}
                                loading="lazy"
                            />
                        </div>
                        
                        <div className="suggestion-content">
                            <div className="suggestion-header">
                                <h4 className="suggestion-name">{product.name}</h4>
                                <span className="suggestion-category">{product.categoryName}</span>
                            </div>
                            
                            <div className="suggestion-pricing">
                                {product.discountPrice && product.discountPrice < product.price ? (
                                    <>
                                        <span className="suggestion-price current">
                                            {formatPrice(product.discountPrice)}
                                        </span>
                                        <span className="suggestion-price original">
                                            {formatPrice(product.price)}
                                        </span>
                                        <span className="suggestion-discount">
                                            {Math.round(((product.price - product.discountPrice) / product.price) * 100)}% off
                                        </span>
                                    </>
                                ) : (
                                    <span className="suggestion-price current">
                                        {formatPrice(product.price)}
                                    </span>
                                )}
                            </div>
                            
                            {product.featured && (
                                <div className="suggestion-badge">
                                    <svg
                                        width="12"
                                        height="12"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
                                            fill="#F0B21B"
                                        />
                                    </svg>
                                    <span>Featured</span>
                                </div>
                            )}
                        </div>
                        
                        <div className="suggestion-arrow">
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M9 18L15 12L9 6"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </div>
                    </div>
                ))}
            </div>
            
            <div className="suggestions-footer">
                <div className="keyboard-hint">
                    <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>
                </div>
            </div>
        </div>
    );
};

export default SearchSuggestions;
