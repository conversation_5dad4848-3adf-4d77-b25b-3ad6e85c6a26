import React, { useState } from 'react';
import { useCart } from '../../contexts/CartContext';
import CartSidebar from './CartSidebar';

const CartIcon = () => {
    const { getItemCount } = useCart();
    const [isCartOpen, setIsCartOpen] = useState(false);
    const itemCount = getItemCount();

    const toggleCart = () => {
        setIsCartOpen(!isCartOpen);
    };

    return (
        <>
            <div className="cart-icon-container">
                <button
                    className="cart-icon-button"
                    onClick={toggleCart}
                    aria-label={`Shopping cart with ${itemCount} items`}
                >
                    <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="cart-icon"
                    >
                        <path
                            d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z"
                            stroke="#F0B21B"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </svg>
                    {itemCount > 0 && (
                        <span className="cart-badge">
                            {itemCount > 99 ? '99+' : itemCount}
                        </span>
                    )}
                </button>
            </div>

            {/* Cart Sidebar */}
            <CartSidebar
                isOpen={isCartOpen}
                onClose={() => setIsCartOpen(false)}
            />
        </>
    );
};

export default CartIcon;
