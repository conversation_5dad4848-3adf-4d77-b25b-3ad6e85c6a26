// Mock API service for frontend-only application
// This replaces the backend API with local mock data

// Mock data for products
const mockProducts = [
  {
    id: 1,
    name: "Executive Office Table",
    description: "Premium executive office table with modern design and customizable features.",
    basePrice: 500,
    category: "Tables",
    image: "/images/table-1.jpg",
    features: ["Adjustable height", "Premium materials", "Modern design", "Customizable"]
  },
  {
    id: 2,
    name: "Ergonomic Office Chair",
    description: "Comfortable ergonomic office chair with lumbar support and adjustable features.",
    basePrice: 200,
    category: "Chairs",
    image: "/images/chair-1.jpg",
    features: ["Ergonomic design", "Lumbar support", "Adjustable height", "Breathable mesh"]
  },
  {
    id: 3,
    name: "Storage Cabinet",
    description: "Modern storage cabinet with multiple compartments and secure locking system.",
    basePrice: 800,
    category: "Storage",
    image: "/images/cabinet-1.jpg",
    features: ["Multiple compartments", "Secure locks", "Modern design", "Durable materials"]
  }
];

// Mock categories
const mockCategories = [
  { id: 1, name: "Tables", description: "Office tables and desks" },
  { id: 2, name: "Chairs", description: "Office chairs and seating" },
  { id: 3, name: "Storage", description: "Storage solutions and cabinets" },
  { id: 4, name: "Workstations", description: "Complete workstation setups" }
];

// Mock API functions
const api = {
  // Get all products
  getProducts: () => {
    return Promise.resolve({
      success: true,
      data: mockProducts
    });
  },

  // Get product by ID
  getProduct: (id) => {
    const product = mockProducts.find(p => p.id === parseInt(id));
    return Promise.resolve({
      success: true,
      data: product || null
    });
  },

  // Get categories
  getCategories: () => {
    return Promise.resolve({
      success: true,
      data: mockCategories
    });
  },

  // Mock authentication (always succeeds)
  login: (credentials) => {
    return Promise.resolve({
      success: true,
      data: {
        token: 'mock-jwt-token',
        user: {
          id: 1,
          name: 'Demo User',
          email: credentials.email
        }
      }
    });
  },

  // Mock registration (always succeeds)
  register: (userData) => {
    return Promise.resolve({
      success: true,
      data: {
        token: 'mock-jwt-token',
        user: {
          id: 1,
          name: userData.name,
          email: userData.email
        }
      }
    });
  }
};

export default api;
