import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const Payment = () => {
    const [activeTab, setActiveTab] = useState('credit_cards');
    const [formData, setFormData] = useState({
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        cardName: '',
        billingAddress: '',
        city: '',
        zipCode: '',
        country: ''
    });

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        // Handle payment processing
        console.log('Processing payment...', { paymentMethod, formData });
    };

    return (
        <div className="payment-methods-page">
            <div className="container">
                {/* Header Section */}
                <div className="payment-header">
                    <h1>Payment Methods</h1>
                    <div className="header-underline"></div>
                    <p>Choose from our wide range of secure payment options for a seamless shopping experience</p>
                </div>

                {/* Payment Tabs */}
                <div className="payment-tabs">
                    <button
                        className={`payment-tab ${activeTab === 'credit_cards' ? 'active' : ''}`}
                        onClick={() => setActiveTab('credit_cards')}
                    >
                        Credit & Debit Cards
                    </button>
                    <button
                        className={`payment-tab ${activeTab === 'e_wallets' ? 'active' : ''}`}
                        onClick={() => setActiveTab('e_wallets')}
                    >
                        E-Wallets
                    </button>
                    <button
                        className={`payment-tab ${activeTab === 'online_banking' ? 'active' : ''}`}
                        onClick={() => setActiveTab('online_banking')}
                    >
                        Online Banking
                    </button>
                </div>

                {/* Payment Content */}
                <div className="payment-content">
                    {activeTab === 'credit_cards' && (
                        <div className="payment-section">
                            <div className="payment-main">
                                <h2>Credit & Debit Cards</h2>
                                <p>Pay securely with your credit or debit card. We accept all major card providers and ensure your payment information is encrypted.</p>

                                <div className="payment-features">
                                    <div className="feature-item">
                                        <span className="feature-icon">🔒</span>
                                        <span>Secure SSL encryption</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">⚡</span>
                                        <span>Quick and easy checkout</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">💾</span>
                                        <span>Save cards for future purchases</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">🛡️</span>
                                        <span>24/7 fraud protection</span>
                                    </div>
                                </div>
                            </div>

                            <div className="security-features">
                                <h3>Security Features</h3>

                                <div className="security-item">
                                    <div className="security-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#F0B21B"/>
                                            <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <div className="security-info">
                                        <h4>PCI DSS Compliant</h4>
                                        <p>Your card details are protected by industry-standard security protocols</p>
                                    </div>
                                </div>

                                <div className="security-item">
                                    <div className="security-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="10" fill="#F0B21B"/>
                                            <path d="M12 6V12L16 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <div className="security-info">
                                        <h4>3D Secure</h4>
                                        <p>Additional verification for enhanced security on your purchases</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'e_wallets' && (
                        <div className="payment-section">
                            <div className="payment-main">
                                <h2>E-Wallets</h2>
                                <p>Pay quickly and securely using your preferred digital wallet. Fast, convenient, and secure transactions.</p>

                                <div className="payment-features">
                                    <div className="feature-item">
                                        <span className="feature-icon">⚡</span>
                                        <span>Instant payments</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">📱</span>
                                        <span>Mobile-friendly</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">🔐</span>
                                        <span>Encrypted transactions</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">💰</span>
                                        <span>No additional fees</span>
                                    </div>
                                </div>
                            </div>

                            <div className="security-features">
                                <h3>Security Features</h3>

                                <div className="security-item">
                                    <div className="security-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#F0B21B"/>
                                            <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <div className="security-info">
                                        <h4>Secure Authentication</h4>
                                        <p>Multi-factor authentication for enhanced account security</p>
                                    </div>
                                </div>

                                <div className="security-item">
                                    <div className="security-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="10" fill="#F0B21B"/>
                                            <path d="M12 6V12L16 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <div className="security-info">
                                        <h4>Real-time Protection</h4>
                                        <p>Advanced fraud detection and prevention systems</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'online_banking' && (
                        <div className="payment-section">
                            <div className="payment-main">
                                <h2>Online Banking</h2>
                                <p>Pay directly from your bank account with secure online banking. Trusted by millions of customers worldwide.</p>

                                <div className="payment-features">
                                    <div className="feature-item">
                                        <span className="feature-icon">🏦</span>
                                        <span>Direct bank transfer</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">🔒</span>
                                        <span>Bank-level security</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">✅</span>
                                        <span>Instant verification</span>
                                    </div>
                                    <div className="feature-item">
                                        <span className="feature-icon">💳</span>
                                        <span>No card required</span>
                                    </div>
                                </div>
                            </div>

                            <div className="security-features">
                                <h3>Security Features</h3>

                                <div className="security-item">
                                    <div className="security-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#F0B21B"/>
                                            <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <div className="security-info">
                                        <h4>Bank-Grade Encryption</h4>
                                        <p>Your banking information is protected with military-grade encryption</p>
                                    </div>
                                </div>

                                <div className="security-item">
                                    <div className="security-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="10" fill="#F0B21B"/>
                                            <path d="M12 6V12L16 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <div className="security-info">
                                        <h4>Secure Connection</h4>
                                        <p>Direct connection to your bank's secure servers</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Payment;
