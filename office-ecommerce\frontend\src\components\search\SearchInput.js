import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import { usePrice } from '../../hooks/usePrice';
import { getAllProducts } from '../../services/products';
import SearchSuggestions from './SearchSuggestions';

const SearchInput = ({ className = '', placeholder = '' }) => {
    const [query, setQuery] = useState('');
    const [suggestions, setSuggestions] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [selectedIndex, setSelectedIndex] = useState(-1);
    const [searchCache, setSearchCache] = useState(new Map());
    
    const searchRef = useRef(null);
    const inputRef = useRef(null);
    const debounceRef = useRef(null);
    const navigate = useNavigate();
    
    const { t } = useLanguage();
    const { formatSinglePrice } = usePrice();

    // Debounced search function
    const debouncedSearch = useCallback((searchQuery) => {
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
        }
        
        debounceRef.current = setTimeout(() => {
            performSearch(searchQuery);
        }, 300);
    }, []);

    // Perform search with caching
    const performSearch = async (searchQuery) => {
        if (searchQuery.length < 2) {
            setSuggestions([]);
            setShowSuggestions(false);
            return;
        }

        // Check cache first
        const cacheKey = searchQuery.toLowerCase();
        if (searchCache.has(cacheKey)) {
            const cachedResults = searchCache.get(cacheKey);
            setSuggestions(cachedResults);
            setShowSuggestions(true);
            return;
        }

        setIsLoading(true);
        
        try {
            const response = await getAllProducts();
            const filteredProducts = filterProducts(response.products, searchQuery);
            
            // Cache the results
            setSearchCache(prev => {
                const newCache = new Map(prev);
                newCache.set(cacheKey, filteredProducts);
                
                // Limit cache size to 50 entries
                if (newCache.size > 50) {
                    const firstKey = newCache.keys().next().value;
                    newCache.delete(firstKey);
                }
                
                return newCache;
            });
            
            setSuggestions(filteredProducts);
            setShowSuggestions(true);
        } catch (error) {
            console.error('Search error:', error);
            setSuggestions([]);
        } finally {
            setIsLoading(false);
        }
    };

    // Filter products based on search query
    const filterProducts = (products, searchQuery) => {
        const query = searchQuery.toLowerCase().trim();
        
        return products
            .filter(product => {
                const name = product.name.toLowerCase();
                const category = product.categoryName.toLowerCase();
                const description = product.description.toLowerCase();
                
                return name.includes(query) || 
                       category.includes(query) || 
                       description.includes(query);
            })
            .sort((a, b) => {
                // Prioritize exact name matches
                const aNameMatch = a.name.toLowerCase().startsWith(query);
                const bNameMatch = b.name.toLowerCase().startsWith(query);
                
                if (aNameMatch && !bNameMatch) return -1;
                if (!aNameMatch && bNameMatch) return 1;
                
                // Then prioritize category matches
                const aCategoryMatch = a.categoryName.toLowerCase().includes(query);
                const bCategoryMatch = b.categoryName.toLowerCase().includes(query);
                
                if (aCategoryMatch && !bCategoryMatch) return -1;
                if (!aCategoryMatch && bCategoryMatch) return 1;
                
                // Finally sort by name
                return a.name.localeCompare(b.name);
            })
            .slice(0, 6); // Limit to 6 suggestions
    };

    // Handle input change
    const handleInputChange = (e) => {
        const value = e.target.value;
        setQuery(value);
        setSelectedIndex(-1);
        
        if (value.trim()) {
            debouncedSearch(value);
        } else {
            setSuggestions([]);
            setShowSuggestions(false);
        }
    };

    // Handle keyboard navigation
    const handleKeyDown = (e) => {
        if (!showSuggestions || suggestions.length === 0) {
            if (e.key === 'Enter') {
                handleSearch();
            }
            return;
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex(prev => 
                    prev < suggestions.length - 1 ? prev + 1 : prev
                );
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0) {
                    handleSuggestionSelect(suggestions[selectedIndex]);
                } else {
                    handleSearch();
                }
                break;
            case 'Escape':
                setShowSuggestions(false);
                setSelectedIndex(-1);
                inputRef.current?.blur();
                break;
            default:
                break;
        }
    };

    // Handle suggestion selection
    const handleSuggestionSelect = (product) => {
        setQuery(product.name);
        setShowSuggestions(false);
        setSelectedIndex(-1);
        navigate(`/products/${product.id}`);
    };

    // Handle search submission
    const handleSearch = () => {
        if (query.trim()) {
            setShowSuggestions(false);
            navigate(`/products?search=${encodeURIComponent(query.trim())}`);
        }
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();
        handleSearch();
    };

    // Close suggestions when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (searchRef.current && !searchRef.current.contains(event.target)) {
                setShowSuggestions(false);
                setSelectedIndex(-1);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Cleanup debounce on unmount
    useEffect(() => {
        return () => {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
        };
    }, []);

    return (
        <div className={`search-container ${className}`} ref={searchRef}>
            <form onSubmit={handleSubmit} className="search-form">
                <div className="search-input-wrapper">
                    <input
                        ref={inputRef}
                        type="text"
                        value={query}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        onFocus={() => query.length >= 2 && setShowSuggestions(true)}
                        placeholder={placeholder || t('searchProducts')}
                        className="search-input"
                        autoComplete="off"
                    />
                    <button
                        type="submit"
                        className="search-button"
                        disabled={!query.trim()}
                    >
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </svg>
                    </button>
                    
                    {isLoading && (
                        <div className="search-loading">
                            <div className="loading-spinner"></div>
                        </div>
                    )}
                </div>
            </form>

            {showSuggestions && (
                <SearchSuggestions
                    suggestions={suggestions}
                    selectedIndex={selectedIndex}
                    onSelect={handleSuggestionSelect}
                    onClose={() => setShowSuggestions(false)}
                    formatPrice={formatSinglePrice}
                    isLoading={isLoading}
                />
            )}
        </div>
    );
};

export default SearchInput;
