{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\contexts\\\\LanguageContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Language context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LanguageContext = /*#__PURE__*/createContext();\n\n// Language action types\nconst LANGUAGE_ACTIONS = {\n  SET_LANGUAGE: 'SET_LANGUAGE',\n  LOAD_LANGUAGE: 'LOAD_LANGUAGE'\n};\n\n// Translation data\nconst translations = {\n  en: {\n    // Navigation\n    home: 'HOME',\n    products: 'Products',\n    customFurniture: '3D Custom Furniture',\n    gallery: 'Gallery',\n    about: 'About Us',\n    contact: 'Contact Us',\n    payments: 'Payments',\n    // Common\n    search: 'Search',\n    searchProducts: 'Search products...',\n    searchPlaceholder: 'Search for furniture, chairs, desks...',\n    addToCart: 'Add to Cart',\n    buyNow: 'Buy Now',\n    viewDetails: 'View Details',\n    quantity: 'Quantity',\n    price: 'Price',\n    total: 'Total',\n    subtotal: 'Subtotal',\n    tax: 'Tax',\n    shipping: 'Shipping',\n    // Product related\n    featuredProducts: 'Featured Products',\n    newArrivals: 'New Arrivals',\n    bestSellers: 'Best Sellers',\n    productCatalog: 'Product Catalog',\n    allProducts: 'All Products',\n    inStock: 'In Stock',\n    outOfStock: 'Out of Stock',\n    // Filters\n    filters: 'Filters',\n    category: 'Category',\n    priceRange: 'Price Range',\n    sortBy: 'Sort By',\n    clearFilters: 'Clear All Filters',\n    // Cart\n    cart: 'Cart',\n    emptyCart: 'Your cart is empty',\n    removeFromCart: 'Remove from cart',\n    updateQuantity: 'Update quantity',\n    // Checkout\n    checkout: 'Checkout',\n    proceedToCheckout: 'Proceed to Checkout',\n    orderSummary: 'Order Summary',\n    // Messages\n    loading: 'Loading...',\n    error: 'Error',\n    success: 'Success',\n    // Header\n    specialOffer: 'SPECIAL OFFER',\n    offerText: 'Get 25% off premium office furniture collections - Limited time offer ending soon!',\n    shopNow: 'Shop Now',\n    excellenceInDesign: 'EXCELLENCE IN DESIGN'\n  },\n  fil: {\n    // Navigation\n    home: 'TAHANAN',\n    products: 'Mga Produkto',\n    customFurniture: '3D Custom na Muwebles',\n    gallery: 'Galerya',\n    about: 'Tungkol Sa Amin',\n    contact: 'Makipag-ugnayan',\n    payments: 'Mga Bayad',\n    // Common\n    search: 'Maghanap',\n    searchProducts: 'Maghanap ng mga produkto...',\n    addToCart: 'Idagdag sa Cart',\n    buyNow: 'Bilhin Ngayon',\n    viewDetails: 'Tingnan ang Detalye',\n    quantity: 'Dami',\n    price: 'Presyo',\n    total: 'Kabuuan',\n    subtotal: 'Subtotal',\n    tax: 'Buwis',\n    shipping: 'Pagpapadala',\n    // Product related\n    featuredProducts: 'Mga Tampok na Produkto',\n    newArrivals: 'Mga Bagong Dating',\n    bestSellers: 'Mga Pinakamabenta',\n    productCatalog: 'Katalogo ng Produkto',\n    allProducts: 'Lahat ng Produkto',\n    inStock: 'May Stock',\n    outOfStock: 'Walang Stock',\n    // Filters\n    filters: 'Mga Filter',\n    category: 'Kategorya',\n    priceRange: 'Saklaw ng Presyo',\n    sortBy: 'Ayusin Ayon Sa',\n    clearFilters: 'Burahin Lahat ng Filter',\n    // Cart\n    cart: 'Cart',\n    emptyCart: 'Walang laman ang inyong cart',\n    removeFromCart: 'Alisin sa cart',\n    updateQuantity: 'I-update ang dami',\n    // Checkout\n    checkout: 'Checkout',\n    proceedToCheckout: 'Magpatuloy sa Checkout',\n    orderSummary: 'Buod ng Order',\n    // Messages\n    loading: 'Naglo-load...',\n    error: 'May Mali',\n    success: 'Tagumpay',\n    // Header\n    specialOffer: 'ESPESYAL NA ALOK',\n    offerText: 'Makakuha ng 25% discount sa premium office furniture collections - Limitadong oras lamang!',\n    shopNow: 'Mamili Ngayon',\n    excellenceInDesign: 'KAHUSAYAN SA DISENYO'\n  }\n};\n\n// Initial state\nconst initialState = {\n  currentLanguage: 'en',\n  languages: [{\n    code: 'en',\n    name: 'English'\n  }, {\n    code: 'fil',\n    name: 'Filipino'\n  }],\n  translations\n};\n\n// Language reducer\nconst languageReducer = (state, action) => {\n  switch (action.type) {\n    case LANGUAGE_ACTIONS.SET_LANGUAGE:\n      return {\n        ...state,\n        currentLanguage: action.payload\n      };\n    case LANGUAGE_ACTIONS.LOAD_LANGUAGE:\n      return {\n        ...state,\n        currentLanguage: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Language provider component\nexport const LanguageProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(languageReducer, initialState);\n\n  // Load language from localStorage on mount\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('selected-language');\n    if (savedLanguage) {\n      dispatch({\n        type: LANGUAGE_ACTIONS.LOAD_LANGUAGE,\n        payload: savedLanguage\n      });\n    }\n  }, []);\n\n  // Save language to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('selected-language', state.currentLanguage);\n  }, [state.currentLanguage]);\n\n  // Set language\n  const setLanguage = languageCode => {\n    dispatch({\n      type: LANGUAGE_ACTIONS.SET_LANGUAGE,\n      payload: languageCode\n    });\n  };\n\n  // Get translation for a key\n  const t = key => {\n    return state.translations[state.currentLanguage][key] || key;\n  };\n\n  // Get current language info\n  const getCurrentLanguage = () => {\n    return state.languages.find(l => l.code === state.currentLanguage) || state.languages[0];\n  };\n  const value = {\n    // State\n    currentLanguage: state.currentLanguage,\n    languages: state.languages,\n    // Actions\n    setLanguage,\n    // Utilities\n    t,\n    getCurrentLanguage\n  };\n  return /*#__PURE__*/_jsxDEV(LanguageContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 213,\n    columnNumber: 9\n  }, this);\n};\n\n// Custom hook to use language context\n_s(LanguageProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = LanguageProvider;\nexport const useLanguage = () => {\n  _s2();\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n_s2(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default LanguageContext;\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "jsxDEV", "_jsxDEV", "LanguageContext", "LANGUAGE_ACTIONS", "SET_LANGUAGE", "LOAD_LANGUAGE", "translations", "en", "home", "products", "customFurniture", "gallery", "about", "contact", "payments", "search", "searchProducts", "searchPlaceholder", "addToCart", "buyNow", "viewDetails", "quantity", "price", "total", "subtotal", "tax", "shipping", "featuredProducts", "newArrivals", "bestSellers", "productCatalog", "allProducts", "inStock", "outOfStock", "filters", "category", "priceRange", "sortBy", "clearFilters", "cart", "emptyCart", "removeFromCart", "updateQuantity", "checkout", "proceedToCheckout", "orderSummary", "loading", "error", "success", "specialOffer", "offerText", "shopNow", "excellenceInDesign", "fil", "initialState", "currentLanguage", "languages", "code", "name", "languageReducer", "state", "action", "type", "payload", "LanguageProvider", "children", "_s", "dispatch", "savedLanguage", "localStorage", "getItem", "setItem", "setLanguage", "languageCode", "t", "key", "getCurrentLanguage", "find", "l", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useLanguage", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/contexts/LanguageContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Language context\nconst LanguageContext = createContext();\n\n// Language action types\nconst LANGUAGE_ACTIONS = {\n    SET_LANGUAGE: 'SET_LANGUAGE',\n    LOAD_LANGUAGE: 'LOAD_LANGUAGE'\n};\n\n// Translation data\nconst translations = {\n    en: {\n        // Navigation\n        home: 'HOME',\n        products: 'Products',\n        customFurniture: '3D Custom Furniture',\n        gallery: 'Gallery',\n        about: 'About Us',\n        contact: 'Contact Us',\n        payments: 'Payments',\n        \n        // Common\n        search: 'Search',\n        searchProducts: 'Search products...',\n        searchPlaceholder: 'Search for furniture, chairs, desks...',\n        addToCart: 'Add to Cart',\n        buyNow: 'Buy Now',\n        viewDetails: 'View Details',\n        quantity: 'Quantity',\n        price: 'Price',\n        total: 'Total',\n        subtotal: 'Subtotal',\n        tax: 'Tax',\n        shipping: 'Shipping',\n        \n        // Product related\n        featuredProducts: 'Featured Products',\n        newArrivals: 'New Arrivals',\n        bestSellers: 'Best Sellers',\n        productCatalog: 'Product Catalog',\n        allProducts: 'All Products',\n        inStock: 'In Stock',\n        outOfStock: 'Out of Stock',\n        \n        // Filters\n        filters: 'Filters',\n        category: 'Category',\n        priceRange: 'Price Range',\n        sortBy: 'Sort By',\n        clearFilters: 'Clear All Filters',\n        \n        // Cart\n        cart: 'Cart',\n        emptyCart: 'Your cart is empty',\n        removeFromCart: 'Remove from cart',\n        updateQuantity: 'Update quantity',\n        \n        // Checkout\n        checkout: 'Checkout',\n        proceedToCheckout: 'Proceed to Checkout',\n        orderSummary: 'Order Summary',\n        \n        // Messages\n        loading: 'Loading...',\n        error: 'Error',\n        success: 'Success',\n        \n        // Header\n        specialOffer: 'SPECIAL OFFER',\n        offerText: 'Get 25% off premium office furniture collections - Limited time offer ending soon!',\n        shopNow: 'Shop Now',\n        excellenceInDesign: 'EXCELLENCE IN DESIGN'\n    },\n    fil: {\n        // Navigation\n        home: 'TAHANAN',\n        products: 'Mga Produkto',\n        customFurniture: '3D Custom na Muwebles',\n        gallery: 'Galerya',\n        about: 'Tungkol Sa Amin',\n        contact: 'Makipag-ugnayan',\n        payments: 'Mga Bayad',\n        \n        // Common\n        search: 'Maghanap',\n        searchProducts: 'Maghanap ng mga produkto...',\n        addToCart: 'Idagdag sa Cart',\n        buyNow: 'Bilhin Ngayon',\n        viewDetails: 'Tingnan ang Detalye',\n        quantity: 'Dami',\n        price: 'Presyo',\n        total: 'Kabuuan',\n        subtotal: 'Subtotal',\n        tax: 'Buwis',\n        shipping: 'Pagpapadala',\n        \n        // Product related\n        featuredProducts: 'Mga Tampok na Produkto',\n        newArrivals: 'Mga Bagong Dating',\n        bestSellers: 'Mga Pinakamabenta',\n        productCatalog: 'Katalogo ng Produkto',\n        allProducts: 'Lahat ng Produkto',\n        inStock: 'May Stock',\n        outOfStock: 'Walang Stock',\n        \n        // Filters\n        filters: 'Mga Filter',\n        category: 'Kategorya',\n        priceRange: 'Saklaw ng Presyo',\n        sortBy: 'Ayusin Ayon Sa',\n        clearFilters: 'Burahin Lahat ng Filter',\n        \n        // Cart\n        cart: 'Cart',\n        emptyCart: 'Walang laman ang inyong cart',\n        removeFromCart: 'Alisin sa cart',\n        updateQuantity: 'I-update ang dami',\n        \n        // Checkout\n        checkout: 'Checkout',\n        proceedToCheckout: 'Magpatuloy sa Checkout',\n        orderSummary: 'Buod ng Order',\n        \n        // Messages\n        loading: 'Naglo-load...',\n        error: 'May Mali',\n        success: 'Tagumpay',\n        \n        // Header\n        specialOffer: 'ESPESYAL NA ALOK',\n        offerText: 'Makakuha ng 25% discount sa premium office furniture collections - Limitadong oras lamang!',\n        shopNow: 'Mamili Ngayon',\n        excellenceInDesign: 'KAHUSAYAN SA DISENYO'\n    }\n};\n\n// Initial state\nconst initialState = {\n    currentLanguage: 'en',\n    languages: [\n        { code: 'en', name: 'English' },\n        { code: 'fil', name: 'Filipino' }\n    ],\n    translations\n};\n\n// Language reducer\nconst languageReducer = (state, action) => {\n    switch (action.type) {\n        case LANGUAGE_ACTIONS.SET_LANGUAGE:\n            return {\n                ...state,\n                currentLanguage: action.payload\n            };\n        case LANGUAGE_ACTIONS.LOAD_LANGUAGE:\n            return {\n                ...state,\n                currentLanguage: action.payload\n            };\n        default:\n            return state;\n    }\n};\n\n// Language provider component\nexport const LanguageProvider = ({ children }) => {\n    const [state, dispatch] = useReducer(languageReducer, initialState);\n\n    // Load language from localStorage on mount\n    useEffect(() => {\n        const savedLanguage = localStorage.getItem('selected-language');\n        if (savedLanguage) {\n            dispatch({ type: LANGUAGE_ACTIONS.LOAD_LANGUAGE, payload: savedLanguage });\n        }\n    }, []);\n\n    // Save language to localStorage whenever it changes\n    useEffect(() => {\n        localStorage.setItem('selected-language', state.currentLanguage);\n    }, [state.currentLanguage]);\n\n    // Set language\n    const setLanguage = (languageCode) => {\n        dispatch({ type: LANGUAGE_ACTIONS.SET_LANGUAGE, payload: languageCode });\n    };\n\n    // Get translation for a key\n    const t = (key) => {\n        return state.translations[state.currentLanguage][key] || key;\n    };\n\n    // Get current language info\n    const getCurrentLanguage = () => {\n        return state.languages.find(l => l.code === state.currentLanguage) || state.languages[0];\n    };\n\n    const value = {\n        // State\n        currentLanguage: state.currentLanguage,\n        languages: state.languages,\n        \n        // Actions\n        setLanguage,\n        \n        // Utilities\n        t,\n        getCurrentLanguage\n    };\n\n    return (\n        <LanguageContext.Provider value={value}>\n            {children}\n        </LanguageContext.Provider>\n    );\n};\n\n// Custom hook to use language context\nexport const useLanguage = () => {\n    const context = useContext(LanguageContext);\n    if (!context) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n};\n\nexport default LanguageContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;;AAE/E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,gBAAGN,aAAa,CAAC,CAAC;;AAEvC;AACA,MAAMO,gBAAgB,GAAG;EACrBC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE;AACnB,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACjBC,EAAE,EAAE;IACA;IACAC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,qBAAqB;IACtCC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,UAAU;IAEpB;IACAC,MAAM,EAAE,QAAQ;IAChBC,cAAc,EAAE,oBAAoB;IACpCC,iBAAiB,EAAE,wCAAwC;IAC3DC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,cAAc;IAC3BC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE,UAAU;IAEpB;IACAC,gBAAgB,EAAE,mBAAmB;IACrCC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,iBAAiB;IACjCC,WAAW,EAAE,cAAc;IAC3BC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,cAAc;IAE1B;IACAC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,aAAa;IACzBC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,mBAAmB;IAEjC;IACAC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,oBAAoB;IAC/BC,cAAc,EAAE,kBAAkB;IAClCC,cAAc,EAAE,iBAAiB;IAEjC;IACAC,QAAQ,EAAE,UAAU;IACpBC,iBAAiB,EAAE,qBAAqB;IACxCC,YAAY,EAAE,eAAe;IAE7B;IACAC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,SAAS;IAElB;IACAC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,oFAAoF;IAC/FC,OAAO,EAAE,UAAU;IACnBC,kBAAkB,EAAE;EACxB,CAAC;EACDC,GAAG,EAAE;IACD;IACA7C,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,cAAc;IACxBC,eAAe,EAAE,uBAAuB;IACxCC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE,WAAW;IAErB;IACAC,MAAM,EAAE,UAAU;IAClBC,cAAc,EAAE,6BAA6B;IAC7CE,SAAS,EAAE,iBAAiB;IAC5BC,MAAM,EAAE,eAAe;IACvBC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,OAAO;IACZC,QAAQ,EAAE,aAAa;IAEvB;IACAC,gBAAgB,EAAE,wBAAwB;IAC1CC,WAAW,EAAE,mBAAmB;IAChCC,WAAW,EAAE,mBAAmB;IAChCC,cAAc,EAAE,sBAAsB;IACtCC,WAAW,EAAE,mBAAmB;IAChCC,OAAO,EAAE,WAAW;IACpBC,UAAU,EAAE,cAAc;IAE1B;IACAC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,WAAW;IACrBC,UAAU,EAAE,kBAAkB;IAC9BC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,yBAAyB;IAEvC;IACAC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,8BAA8B;IACzCC,cAAc,EAAE,gBAAgB;IAChCC,cAAc,EAAE,mBAAmB;IAEnC;IACAC,QAAQ,EAAE,UAAU;IACpBC,iBAAiB,EAAE,wBAAwB;IAC3CC,YAAY,EAAE,eAAe;IAE7B;IACAC,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,UAAU;IAEnB;IACAC,YAAY,EAAE,kBAAkB;IAChCC,SAAS,EAAE,4FAA4F;IACvGC,OAAO,EAAE,eAAe;IACxBC,kBAAkB,EAAE;EACxB;AACJ,CAAC;;AAED;AACA,MAAME,YAAY,GAAG;EACjBC,eAAe,EAAE,IAAI;EACrBC,SAAS,EAAE,CACP;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAU,CAAC,EAC/B;IAAED,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAW,CAAC,CACpC;EACDpD;AACJ,CAAC;;AAED;AACA,MAAMqD,eAAe,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACvC,QAAQA,MAAM,CAACC,IAAI;IACf,KAAK3D,gBAAgB,CAACC,YAAY;MAC9B,OAAO;QACH,GAAGwD,KAAK;QACRL,eAAe,EAAEM,MAAM,CAACE;MAC5B,CAAC;IACL,KAAK5D,gBAAgB,CAACE,aAAa;MAC/B,OAAO;QACH,GAAGuD,KAAK;QACRL,eAAe,EAAEM,MAAM,CAACE;MAC5B,CAAC;IACL;MACI,OAAOH,KAAK;EACpB;AACJ,CAAC;;AAED;AACA,OAAO,MAAMI,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACN,KAAK,EAAEO,QAAQ,CAAC,GAAGrE,UAAU,CAAC6D,eAAe,EAAEL,YAAY,CAAC;;EAEnE;EACAvD,SAAS,CAAC,MAAM;IACZ,MAAMqE,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC/D,IAAIF,aAAa,EAAE;MACfD,QAAQ,CAAC;QAAEL,IAAI,EAAE3D,gBAAgB,CAACE,aAAa;QAAE0D,OAAO,EAAEK;MAAc,CAAC,CAAC;IAC9E;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArE,SAAS,CAAC,MAAM;IACZsE,YAAY,CAACE,OAAO,CAAC,mBAAmB,EAAEX,KAAK,CAACL,eAAe,CAAC;EACpE,CAAC,EAAE,CAACK,KAAK,CAACL,eAAe,CAAC,CAAC;;EAE3B;EACA,MAAMiB,WAAW,GAAIC,YAAY,IAAK;IAClCN,QAAQ,CAAC;MAAEL,IAAI,EAAE3D,gBAAgB,CAACC,YAAY;MAAE2D,OAAO,EAAEU;IAAa,CAAC,CAAC;EAC5E,CAAC;;EAED;EACA,MAAMC,CAAC,GAAIC,GAAG,IAAK;IACf,OAAOf,KAAK,CAACtD,YAAY,CAACsD,KAAK,CAACL,eAAe,CAAC,CAACoB,GAAG,CAAC,IAAIA,GAAG;EAChE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,OAAOhB,KAAK,CAACJ,SAAS,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrB,IAAI,KAAKG,KAAK,CAACL,eAAe,CAAC,IAAIK,KAAK,CAACJ,SAAS,CAAC,CAAC,CAAC;EAC5F,CAAC;EAED,MAAMuB,KAAK,GAAG;IACV;IACAxB,eAAe,EAAEK,KAAK,CAACL,eAAe;IACtCC,SAAS,EAAEI,KAAK,CAACJ,SAAS;IAE1B;IACAgB,WAAW;IAEX;IACAE,CAAC;IACDE;EACJ,CAAC;EAED,oBACI3E,OAAA,CAACC,eAAe,CAAC8E,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAd,QAAA,EAClCA;EAAQ;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAEnC,CAAC;;AAED;AAAAlB,EAAA,CAnDaF,gBAAgB;AAAAqB,EAAA,GAAhBrB,gBAAgB;AAoD7B,OAAO,MAAMsB,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAMC,OAAO,GAAG3F,UAAU,CAACK,eAAe,CAAC;EAC3C,IAAI,CAACsF,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACzE;EACA,OAAOD,OAAO;AAClB,CAAC;AAACD,GAAA,CANWD,WAAW;AAQxB,eAAepF,eAAe;AAAC,IAAAmF,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}