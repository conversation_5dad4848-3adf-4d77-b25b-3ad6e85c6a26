import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
    return (
        <footer className="footer">
            <div className="container">
                <div className="footer-content">
                    {/* Left Side - Contact Information */}
                    <div className="footer-left">
                        <h2>Get Touch in Excellence</h2>
                        <p>Have questions about our premium office solutions? Our team is here to help you create the perfect workspace.</p>

                        <div className="contact-methods">
                            <div className="contact-method">
                                <div className="method-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="2" y="4" width="20" height="16" rx="2" fill="white" stroke="white" strokeWidth="2"/>
                                        <path d="M22 6L12 13L2 6" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        <rect x="2" y="4" width="20" height="16" rx="2" fill="none" stroke="#F0B21B" strokeWidth="2"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4>Email Us</h4>
                                    <p><EMAIL></p>
                                </div>
                            </div>

                            <div className="contact-method">
                                <div className="method-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M22 16.92V19.92C22 20.51 21.39 21 20.8 21H19.82C16.75 20.67 13.79 19.61 11.19 17.93C8.77 16.39 6.72 14.34 5.18 11.92C3.5 9.32 2.44 6.36 2.11 3.29C2.05 2.7 2.49 2 3.08 2H6.08C6.67 2 7.18 2.51 7.18 3.1V3.72C7.31 4.68 7.54 5.62 7.88 6.53C8.01 6.89 7.94 7.28 7.69 7.58L6.42 8.85C7.85 11.35 9.93 13.43 12.43 14.86L13.7 13.59C14 13.34 14.39 13.27 14.75 13.4C15.66 13.74 16.6 13.97 17.56 14.1H17.56C18.15 14.1 18.66 14.61 18.66 15.2V18.2C18.66 18.79 18.15 19.3 17.56 19.3H16.92Z" fill="white" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4>Call Us</h4>
                                    <p>+1 234 567 890</p>
                                </div>
                            </div>

                            <div className="contact-method">
                                <div className="method-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61 3.95 5.32 5.64 3.64C7.32 1.95 9.61 1 12 1C14.39 1 16.68 1.95 18.36 3.64C20.05 5.32 21 7.61 21 10Z" fill="white" stroke="#F0B21B" strokeWidth="2"/>
                                        <circle cx="12" cy="10" r="3" fill="#F0B21B"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4>Visit Us</h4>
                                    <p>123 Design Street, Creative City 12345</p>
                                </div>
                            </div>
                        </div>

                        <div className="company-tagline">
                            <p>Creating exceptional office environments through innovative furniture solutions.</p>
                        </div>
                    </div>

                    {/* Right Side - Contact Form */}
                    <div className="footer-right">
                        <div className="contact-form-header">
                            <span className="contact-form-badge">Contact Form</span>
                        </div>

                        <form className="footer-contact-form">
                            <div className="form-group">
                                <input type="text" placeholder="Enter your name" required />
                            </div>
                            <div className="form-group">
                                <input type="email" placeholder="Enter your email" required />
                            </div>
                            <div className="form-group">
                                <textarea placeholder="Message" rows="4" required></textarea>
                            </div>
                            <button type="submit" className="send-message-btn">Send Message</button>
                        </form>
                    </div>
                </div>

                {/* Bottom Section */}
                <div className="footer-bottom">
                    <div className="footer-bottom-content">
                        <div className="footer-links">
                            <div className="footer-section">
                                <h4>Quick Links</h4>
                                <ul>
                                    <li><Link to="/about">About Us</Link></li>
                                    <li><Link to="/products">Products</Link></li>
                                </ul>
                            </div>

                            <div className="footer-section">
                                <h4>Support</h4>
                                <ul>
                                    <li><Link to="/faq">FAQ</Link></li>
                                    <li><Link to="/privacy">Privacy Policy</Link></li>
                                    <li><Link to="/terms">Terms of Service</Link></li>
                                </ul>
                            </div>

                            <div className="footer-section">
                                <h4>Follow Us</h4>
                                <div className="social-links">
                                    <a href="#facebook" aria-label="Facebook" className="social-link">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18 2H15C13.6739 2 12.4021 2.52678 11.4645 3.46447C10.5268 4.40215 10 5.67392 10 7V10H7V14H10V22H14V14H17L18 10H14V7C14 6.73478 14.1054 6.48043 14.2929 6.29289C14.4804 6.10536 14.7348 6 15 6H18V2Z" fill="white"/>
                                        </svg>
                                    </a>
                                    <a href="#instagram" aria-label="Instagram" className="social-link">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="white" strokeWidth="2" fill="none"/>
                                            <path d="M16 11.37C16.1234 12.2022 15.9813 13.0522 15.5938 13.799C15.2063 14.5458 14.5931 15.1514 13.8416 15.5297C13.0901 15.9079 12.2384 16.0396 11.4078 15.9059C10.5771 15.7723 9.80976 15.3801 9.21484 14.7852C8.61992 14.1902 8.22773 13.4229 8.09407 12.5922C7.9604 11.7615 8.09207 10.9099 8.47033 10.1584C8.84859 9.40685 9.45419 8.79374 10.201 8.40624C10.9478 8.01874 11.7978 7.87658 12.63 8C13.4789 8.12588 14.2649 8.52146 14.8717 9.1283C15.4785 9.73515 15.8741 10.5211 16 11.37Z" stroke="white" strokeWidth="2" fill="none"/>
                                            <path d="M17.5 6.5H17.51" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
