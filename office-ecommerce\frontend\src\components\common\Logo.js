import React from 'react';

const Logo = ({ className = "", size = "default" }) => {
    const sizes = {
        small: { width: 120, height: 40 },
        default: { width: 180, height: 60 },
        large: { width: 240, height: 80 }
    };

    const { width, height } = sizes[size] || sizes.default;

    return (
        <div className={`logo-container ${className}`}>
            <svg
                width="100%"
                height="100%"
                viewBox="0 0 240 80"
                xmlns="http://www.w3.org/2000/svg"
                className="design-excellence-logo"
                preserveAspectRatio="xMidYMid meet"
                style={{
                    maxWidth: `${width}px`,
                    maxHeight: `${height}px`,
                    width: '100%',
                    height: 'auto'
                }}
            >
                {/* DC Letters */}
                <g className="dc-letters">
                    {/* D Letter */}
                    <path 
                        d="M8 15 L8 65 L25 65 C35 65 42 58 42 40 C42 22 35 15 25 15 Z M18 25 L25 25 C28 25 32 27 32 40 C32 53 28 55 25 55 L18 55 Z" 
                        fill="#4A4A4A"
                        strokeWidth="1"
                        stroke="#4A4A4A"
                    />
                    
                    {/* C Letter */}
                    <path 
                        d="M50 25 C55 20 62 20 67 25 L72 18 C65 12 55 12 50 18 C45 24 45 36 45 40 C45 44 45 56 50 62 C55 68 65 68 72 62 L67 55 C62 60 55 60 50 55 C47 52 47 48 47 40 C47 32 47 28 50 25 Z" 
                        fill="#F59E0B"
                        strokeWidth="1"
                        stroke="#F59E0B"
                    />
                </g>

                {/* DESIGN EXCELLENCE Text */}
                <g className="main-text">
                    <text x="95" y="35" fontSize="16" fontWeight="bold" fill="#4A4A4A" fontFamily="Arial, sans-serif">
                        DESIGN
                    </text>
                    <text x="175" y="35" fontSize="16" fontWeight="bold" fill="#F59E0B" fontFamily="Arial, sans-serif">
                        EXCELLENCE
                    </text>
                </g>

                {/* Horizontal Line */}
                <line x1="95" y1="45" x2="235" y2="45" stroke="#4A4A4A" strokeWidth="2"/>

                {/* Subtitle */}
                <text x="95" y="62" fontSize="10" fill="#4A4A4A" fontFamily="Arial, sans-serif" letterSpacing="2">
                    HOME & OFFICE SYSTEM CO.
                </text>
            </svg>
        </div>
    );
};

export default Logo;
