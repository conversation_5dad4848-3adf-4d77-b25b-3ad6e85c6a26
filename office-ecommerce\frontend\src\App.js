import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './hooks/useAuth';
import { CartProvider } from './contexts/CartContext';
import { CurrencyProvider } from './contexts/CurrencyContext';
import { LanguageProvider } from './contexts/LanguageContext';
import Header from './components/common/Header';
import Footer from './components/common/Footer';
import Home from './pages/Home';
import Login from './pages/Login';
import ProductCatalog from './pages/ProductCatalog';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Payment from './pages/Payment';
import About from './pages/About';
import Gallery from './pages/Gallery';
import ProductCardDemo from './components/demo/ProductCardDemo';
import './styles/globals.css';

function App() {
    return (
        <AuthProvider>
            <CurrencyProvider>
                <LanguageProvider>
                    <CartProvider>
                        <Router>
                            <div className="App">
                                <Header />
                                <main>
                                    <Routes>
                                        <Route path="/" element={<Home />} />
                                        <Route path="/login" element={<Login />} />
                                        <Route path="/products" element={<ProductCatalog />} />
                                        <Route path="/product/:id" element={<ProductDetail />} />
                                        <Route path="/products/:id" element={<ProductDetail />} />
                                        <Route path="/cart" element={<Cart />} />
                                        <Route path="/payment" element={<Payment />} />
                                        <Route path="/about" element={<About />} />
                                        <Route path="/gallery" element={<Gallery />} />
                                        <Route path="/demo" element={<ProductCardDemo />} />
                                    </Routes>
                                </main>
                                <Footer />
                            </div>
                        </Router>
                    </CartProvider>
                </LanguageProvider>
            </CurrencyProvider>
        </AuthProvider>
    );
}

export default App;