import api from './api';

export const authService = {
    async login(email, password) {
        const response = await api.login({ email, password });
        return response;
    },

    async register(userData) {
        const response = await api.register(userData);
        return response;
    },

    async getProfile() {
        // Mock profile data
        return {
            success: true,
            data: {
                id: 1,
                name: 'Demo User',
                email: '<EMAIL>',
                phone: '(*************',
                address: '123 Demo Street, Demo City, DC 12345'
            }
        };
    },

    async updateProfile(profileData) {
        // Mock profile update (always succeeds)
        return {
            success: true,
            data: {
                ...profileData,
                id: 1
            }
        };
    }
};
