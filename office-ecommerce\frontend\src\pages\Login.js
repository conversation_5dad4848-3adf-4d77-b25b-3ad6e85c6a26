import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import '../styles/pages.css';

const Login = () => {
    const [isLogin, setIsLogin] = useState(true);
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        phone: ''
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    
    const { login, register } = useAuth();
    const navigate = useNavigate();

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            if (isLogin) {
                await login(formData.email, formData.password);
            } else {
                await register(formData);
            }
            navigate('/');
        } catch (err) {
            setError(err.message || 'An error occurred');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="auth-page">
            <div className="auth-container">
                <div className="auth-content">
                    <div className="auth-form-section">
                        <div className="auth-header">
                            <h1>{isLogin ? 'Welcome Back' : 'New Here?'}</h1>
                            {isLogin ? (
                                <div className="auth-description">
                                    <p>Email Address</p>
                                    <p>Password</p>
                                </div>
                            ) : (
                                <p>Create an account to enjoy a personalized shopping experience, faster checkout, and exclusive benefits.</p>
                            )}
                        </div>

                        <form onSubmit={handleSubmit} className="auth-form">
                            {error && <div className="error-message">{error}</div>}
                            
                            {!isLogin && (
                                <>
                                    <div className="form-row">
                                        <div className="form-group">
                                            <input
                                                type="text"
                                                name="firstName"
                                                placeholder="First Name"
                                                value={formData.firstName}
                                                onChange={handleChange}
                                                required
                                            />
                                        </div>
                                        <div className="form-group">
                                            <input
                                                type="text"
                                                name="lastName"
                                                placeholder="Last Name"
                                                value={formData.lastName}
                                                onChange={handleChange}
                                                required
                                            />
                                        </div>
                                    </div>
                                    <div className="form-group">
                                        <input
                                            type="tel"
                                            name="phone"
                                            placeholder="Phone Number"
                                            value={formData.phone}
                                            onChange={handleChange}
                                        />
                                    </div>
                                </>
                            )}
                            
                            <div className="form-group">
                                <input
                                    type="email"
                                    name="email"
                                    placeholder="Email Address"
                                    value={formData.email}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            
                            <div className="form-group">
                                <input
                                    type="password"
                                    name="password"
                                    placeholder="Password"
                                    value={formData.password}
                                    onChange={handleChange}
                                    required
                                />
                            </div>

                            {isLogin && (
                                <div className="form-options">
                                    <Link to="/forgot-password" className="forgot-link">
                                        Forgot Password?
                                    </Link>
                                </div>
                            )}

                            <button type="submit" className="auth-submit-btn" disabled={loading}>
                                {loading ? 'Please wait...' : (isLogin ? 'Sign In →' : 'Create Account →')}
                            </button>
                        </form>

                        {!isLogin && (
                            <div className="terms-notice">
                                <p>
                                    By creating an account, you agree to our{' '}
                                    <Link to="/terms">Terms</Link> and{' '}
                                    <Link to="/privacy">Privacy Policy</Link>
                                </p>
                            </div>
                        )}
                    </div>

                    <div className="auth-switch-section">
                        <div className="switch-content">
                            <h2>{isLogin ? 'New Here?' : 'Welcome Back'}</h2>
                            <p>
                                {isLogin 
                                    ? 'Create an account to enjoy a personalized shopping experience, faster checkout, and exclusive benefits.'
                                    : 'Sign in to access your account and continue your shopping experience.'
                                }
                            </p>
                            <button 
                                className="switch-btn"
                                onClick={() => {
                                    setIsLogin(!isLogin);
                                    setError('');
                                    setFormData({
                                        email: '',
                                        password: '',
                                        firstName: '',
                                        lastName: '',
                                        phone: ''
                                    });
                                }}
                            >
                                {isLogin ? 'Create Account →' : 'Sign In →'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;