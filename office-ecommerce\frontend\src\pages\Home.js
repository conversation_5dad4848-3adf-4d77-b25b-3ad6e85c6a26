import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import ProductCard from '../components/product/ProductCard';
import { getFeaturedProducts } from '../services/products';
import '../styles/pages.css';

const Home = () => {
    const [featuredProducts, setFeaturedProducts] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadFeaturedProducts();
    }, []);

    const loadFeaturedProducts = async () => {
        try {
            const response = await getFeaturedProducts();
            // Handle both API response and mock data structure
            const products = response.products || response;
            setFeaturedProducts(products);
        } catch (error) {
            console.error('Error loading featured products:', error);
        } finally {
            setLoading(false);
        }
    };

    const categories = [
        {
            id: 'desks',
            name: 'Office Desks',
            count: 12,
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="4" width="18" height="12" rx="2" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M3 10h18" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M8 21v-7" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M16 21v-7" stroke="#F0B21B" strokeWidth="2"/>
                </svg>
            ),
            categoryName: 'Desk'
        },
        {
            id: 'chairs',
            name: 'Office Chairs',
            count: 8,
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12V7a7 7 0 0 1 14 0v5" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M22 19H2l2-7h16l2 7Z" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M6 19v2" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M18 19v2" stroke="#F0B21B" strokeWidth="2"/>
                </svg>
            ),
            categoryName: 'Chair'
        },
        {
            id: 'storage',
            name: 'Storage Solutions',
            count: 15,
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="#F0B21B" strokeWidth="2"/>
                    <polyline points="14,2 14,8 20,8" stroke="#F0B21B" strokeWidth="2"/>
                    <line x1="16" y1="13" x2="8" y2="13" stroke="#F0B21B" strokeWidth="2"/>
                    <line x1="16" y1="17" x2="8" y2="17" stroke="#F0B21B" strokeWidth="2"/>
                    <polyline points="10,9 9,9 8,9" stroke="#F0B21B" strokeWidth="2"/>
                </svg>
            ),
            categoryName: 'Storage'
        },
        {
            id: 'conference',
            name: 'Conference Furniture',
            count: 6,
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 21h18" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M5 21V7l8-4v18" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M19 21V11l-6-4" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M9 9v.01" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M9 12v.01" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M9 15v.01" stroke="#F0B21B" strokeWidth="2"/>
                    <path d="M9 18v.01" stroke="#F0B21B" strokeWidth="2"/>
                </svg>
            ),
            categoryName: 'Conference'
        },
        {
            id: 'accessories',
            name: 'Office Accessories',
            count: 20,
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48" stroke="#F0B21B" strokeWidth="2"/>
                </svg>
            ),
            categoryName: 'Accessories'
        }
    ];

    return (
        <div className="home">
            {/* Hero Section */}
            <section className="hero">
                <div className="hero-content">
                    <div className="hero-text">
                        <h1>Premium Office Furniture Solutions</h1>
                        <p>Transform your workspace with our premium collection of office furniture</p>
                        <p>Discover our premium collection of office furniture designed for modern professionals</p>
                        <Link to="/products" className="cta-button">SHOP NOW</Link>
                    </div>
                </div>
                <div className="hero-navigation">
                    <button className="nav-arrow left">‹</button>
                    <button className="nav-arrow right">›</button>
                </div>
                <div className="hero-indicators">
                    <span className="indicator active"></span>
                    <span className="indicator"></span>
                    <span className="indicator"></span>
                </div>
            </section>

            {/* Featured Categories */}
            <section className="featured-categories">
                <div className="container">
                    <div className="section-header">
                        <h2>Featured Categories</h2>
                        <Link to="/products" className="view-all">View All Categories →</Link>
                    </div>
                    <div className="categories-grid">
                        {categories.map((category, index) => (
                            <Link
                                key={index}
                                to={`/products?category=${encodeURIComponent(category.categoryName)}`}
                                className="category-card"
                            >
                                <div className="category-icon">{category.icon}</div>
                                <h3>{category.name}</h3>
                                <p>{category.count} Products</p>
                            </Link>
                        ))}
                    </div>
                </div>
            </section>

            {/* Featured Products */}
            <section className="featured-products">
                <div className="container">
                    <div className="section-header">
                        <h2>Featured Products</h2>
                        <Link to="/products" className="view-all">View All Products →</Link>
                    </div>
                    {loading ? (
                        <div className="loading">Loading featured products...</div>
                    ) : (
                        <div className="products-grid">
                            {featuredProducts.map(product => (
                                <ProductCard key={product.id} product={product} />
                            ))}
                        </div>
                    )}
                </div>
            </section>

            {/* Testimonials Section */}
            <section className="testimonials">
                <div className="container">
                    <div className="testimonials-header">
                        <span className="testimonials-label">TESTIMONIALS</span>
                        <h2>What Our Clients Say</h2>
                        <div className="testimonials-underline"></div>
                    </div>

                    <div className="testimonials-slider">
                        <button className="testimonial-nav prev" aria-label="Previous testimonial">‹</button>

                        <div className="testimonial-content">
                            <div className="testimonial-stars">
                                <span className="star">★</span>
                                <span className="star">★</span>
                                <span className="star">★</span>
                                <span className="star">★</span>
                                <span className="star">★</span>
                            </div>

                            <div className="testimonial-avatar">
                                <div className="avatar-circle"></div>
                            </div>

                            <div className="testimonial-quote">
                                <span className="quote-mark">"</span>
                                <p>The ergonomic solutions provided have significantly improved our team's comfort and productivity. Outstanding products!</p>
                            </div>

                            <div className="testimonial-author">
                                <h4>Emily Rodriguez</h4>
                                <p>HR Director, Tech Innovations Ltd</p>
                            </div>
                        </div>

                        <button className="testimonial-nav next" aria-label="Next testimonial">›</button>
                    </div>

                    <div className="testimonials-indicators">
                        <span className="indicator active"></span>
                        <span className="indicator"></span>
                        <span className="indicator"></span>
                    </div>
                </div>
            </section>
        </div>
    );
};

export default Home;