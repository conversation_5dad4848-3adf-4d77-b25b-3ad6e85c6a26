import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import CartItem from './CartItem';

const CartSidebar = ({ isOpen, onClose }) => {
    const { items, getSubtotal, getTax, getShipping, getTotal, clearCart } = useCart();

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    const subtotal = getSubtotal();
    const tax = getTax(subtotal);
    const shipping = getShipping(subtotal);
    const total = getTotal();

    return (
        <>
            {/* Overlay */}
            {isOpen && (
                <div 
                    className="cart-overlay" 
                    onClick={onClose}
                    aria-hidden="true"
                />
            )}

            {/* Sidebar */}
            <div className={`cart-sidebar ${isOpen ? 'open' : ''}`}>
                <div className="cart-header">
                    <h2>Shopping Cart</h2>
                    <button 
                        className="cart-close-btn"
                        onClick={onClose}
                        aria-label="Close cart"
                    >
                        ×
                    </button>
                </div>

                <div className="cart-content">
                    {items.length === 0 ? (
                        <div className="cart-empty">
                            <div className="empty-cart-icon">
                                🛒
                            </div>
                            <h3>Your cart is empty</h3>
                            <p>Add some products to get started!</p>
                            <Link 
                                to="/products" 
                                className="btn btn-primary"
                                onClick={onClose}
                            >
                                Shop Now
                            </Link>
                        </div>
                    ) : (
                        <>
                            {/* Cart Items */}
                            <div className="cart-items">
                                {items.map(item => (
                                    <CartItem 
                                        key={item.id} 
                                        item={item} 
                                    />
                                ))}
                            </div>

                            {/* Cart Summary */}
                            <div className="cart-summary">
                                <div className="summary-row">
                                    <span>Subtotal:</span>
                                    <span>{formatPrice(subtotal)}</span>
                                </div>
                                <div className="summary-row">
                                    <span>Tax:</span>
                                    <span>{formatPrice(tax)}</span>
                                </div>
                                <div className="summary-row">
                                    <span>Shipping:</span>
                                    <span>
                                        {shipping === 0 ? 'FREE' : formatPrice(shipping)}
                                    </span>
                                </div>
                                {shipping === 0 && (
                                    <div className="free-shipping-notice">
                                        🎉 You qualify for free shipping!
                                    </div>
                                )}
                                <div className="summary-row total">
                                    <span>Total:</span>
                                    <span>{formatPrice(total)}</span>
                                </div>
                            </div>

                            {/* Cart Actions */}
                            <div className="cart-actions">
                                <Link 
                                    to="/cart" 
                                    className="btn btn-secondary btn-full"
                                    onClick={onClose}
                                >
                                    View Cart
                                </Link>
                                <Link 
                                    to="/checkout" 
                                    className="btn btn-primary btn-full"
                                    onClick={onClose}
                                >
                                    Checkout
                                </Link>
                                <button 
                                    className="btn btn-text btn-full"
                                    onClick={() => {
                                        if (window.confirm('Are you sure you want to clear your cart?')) {
                                            clearCart();
                                        }
                                    }}
                                >
                                    Clear Cart
                                </button>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </>
    );
};

export default CartSidebar;
