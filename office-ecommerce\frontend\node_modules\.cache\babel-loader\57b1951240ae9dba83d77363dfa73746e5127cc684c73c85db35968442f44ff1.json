{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\search\\\\SearchInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { usePrice } from '../../hooks/usePrice';\nimport { getProducts } from '../../services/products';\nimport SearchSuggestions from './SearchSuggestions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchInput = ({\n  className = '',\n  placeholder = ''\n}) => {\n  _s();\n  const [query, setQuery] = useState('');\n  const [suggestions, setSuggestions] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const [searchCache, setSearchCache] = useState(new Map());\n  const searchRef = useRef(null);\n  const inputRef = useRef(null);\n  const debounceRef = useRef(null);\n  const navigate = useNavigate();\n  const {\n    t\n  } = useLanguage();\n  const {\n    formatSinglePrice\n  } = usePrice();\n\n  // Debounced search function\n  const debouncedSearch = useCallback(searchQuery => {\n    if (debounceRef.current) {\n      clearTimeout(debounceRef.current);\n    }\n    debounceRef.current = setTimeout(() => {\n      performSearch(searchQuery);\n    }, 300);\n  }, []);\n\n  // Perform search with caching\n  const performSearch = async searchQuery => {\n    if (searchQuery.length < 2) {\n      setSuggestions([]);\n      setShowSuggestions(false);\n      return;\n    }\n\n    // Check cache first\n    const cacheKey = searchQuery.toLowerCase();\n    if (searchCache.has(cacheKey)) {\n      const cachedResults = searchCache.get(cacheKey);\n      setSuggestions(cachedResults);\n      setShowSuggestions(true);\n      return;\n    }\n    setIsLoading(true);\n    try {\n      const products = await getProducts();\n      const filteredProducts = filterProducts(products, searchQuery);\n\n      // Cache the results\n      setSearchCache(prev => {\n        const newCache = new Map(prev);\n        newCache.set(cacheKey, filteredProducts);\n\n        // Limit cache size to 50 entries\n        if (newCache.size > 50) {\n          const firstKey = newCache.keys().next().value;\n          newCache.delete(firstKey);\n        }\n        return newCache;\n      });\n      setSuggestions(filteredProducts);\n      setShowSuggestions(true);\n    } catch (error) {\n      console.error('Search error:', error);\n      setSuggestions([]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Filter products based on search query\n  const filterProducts = (products, searchQuery) => {\n    const query = searchQuery.toLowerCase().trim();\n    return products.filter(product => {\n      const name = product.name.toLowerCase();\n      const category = product.categoryName.toLowerCase();\n      const description = product.description.toLowerCase();\n      return name.includes(query) || category.includes(query) || description.includes(query);\n    }).sort((a, b) => {\n      // Prioritize exact name matches\n      const aNameMatch = a.name.toLowerCase().startsWith(query);\n      const bNameMatch = b.name.toLowerCase().startsWith(query);\n      if (aNameMatch && !bNameMatch) return -1;\n      if (!aNameMatch && bNameMatch) return 1;\n\n      // Then prioritize category matches\n      const aCategoryMatch = a.categoryName.toLowerCase().includes(query);\n      const bCategoryMatch = b.categoryName.toLowerCase().includes(query);\n      if (aCategoryMatch && !bCategoryMatch) return -1;\n      if (!aCategoryMatch && bCategoryMatch) return 1;\n\n      // Finally sort by name\n      return a.name.localeCompare(b.name);\n    }).slice(0, 6); // Limit to 6 suggestions\n  };\n\n  // Handle input change\n  const handleInputChange = e => {\n    const value = e.target.value;\n    setQuery(value);\n    setSelectedIndex(-1);\n    if (value.trim()) {\n      debouncedSearch(value);\n    } else {\n      setSuggestions([]);\n      setShowSuggestions(false);\n    }\n  };\n\n  // Handle keyboard navigation\n  const handleKeyDown = e => {\n    var _inputRef$current;\n    if (!showSuggestions || suggestions.length === 0) {\n      if (e.key === 'Enter') {\n        handleSearch();\n      }\n      return;\n    }\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => prev < suggestions.length - 1 ? prev + 1 : prev);\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0) {\n          handleSuggestionSelect(suggestions[selectedIndex]);\n        } else {\n          handleSearch();\n        }\n        break;\n      case 'Escape':\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n        (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.blur();\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Handle suggestion selection\n  const handleSuggestionSelect = product => {\n    setQuery(product.name);\n    setShowSuggestions(false);\n    setSelectedIndex(-1);\n    navigate(`/products/${product.id}`);\n  };\n\n  // Handle search submission\n  const handleSearch = () => {\n    if (query.trim()) {\n      setShowSuggestions(false);\n      navigate(`/products?search=${encodeURIComponent(query.trim())}`);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    handleSearch();\n  };\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (searchRef.current && !searchRef.current.contains(event.target)) {\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  // Cleanup debounce on unmount\n  useEffect(() => {\n    return () => {\n      if (debounceRef.current) {\n        clearTimeout(debounceRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `search-container ${className}`,\n    ref: searchRef,\n    children: [/*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"search-form\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-input-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          ref: inputRef,\n          type: \"text\",\n          value: query,\n          onChange: handleInputChange,\n          onKeyDown: handleKeyDown,\n          onFocus: () => query.length >= 2 && setShowSuggestions(true),\n          placeholder: placeholder || t('searchProducts'),\n          className: \"search-input\",\n          autoComplete: \"off\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"search-button\",\n          disabled: !query.trim(),\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 21\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-loading\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }, this), showSuggestions && /*#__PURE__*/_jsxDEV(SearchSuggestions, {\n      suggestions: suggestions,\n      selectedIndex: selectedIndex,\n      onSelect: handleSuggestionSelect,\n      onClose: () => setShowSuggestions(false),\n      formatPrice: formatSinglePrice,\n      isLoading: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 216,\n    columnNumber: 9\n  }, this);\n};\n_s(SearchInput, \"ZEkZttS6HsyRaHqE8KocgD6Mpt0=\", false, function () {\n  return [useNavigate, useLanguage, usePrice];\n});\n_c = SearchInput;\nexport default SearchInput;\nvar _c;\n$RefreshReg$(_c, \"SearchInput\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "useNavigate", "useLanguage", "usePrice", "getProducts", "SearchSuggestions", "jsxDEV", "_jsxDEV", "SearchInput", "className", "placeholder", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "suggestions", "setSuggestions", "isLoading", "setIsLoading", "showSuggestions", "setShowSuggestions", "selectedIndex", "setSelectedIndex", "searchCache", "setSearchCache", "Map", "searchRef", "inputRef", "debounceRef", "navigate", "t", "formatSinglePrice", "debouncedSearch", "searchQuery", "current", "clearTimeout", "setTimeout", "performSearch", "length", "cache<PERSON>ey", "toLowerCase", "has", "cachedResults", "get", "products", "filteredProducts", "filterProducts", "prev", "newCache", "set", "size", "firstKey", "keys", "next", "value", "delete", "error", "console", "trim", "filter", "product", "name", "category", "categoryName", "description", "includes", "sort", "a", "b", "aNameMatch", "startsWith", "bNameMatch", "aCategoryMatch", "bCategoryMatch", "localeCompare", "slice", "handleInputChange", "e", "target", "handleKeyDown", "_inputRef$current", "key", "handleSearch", "preventDefault", "handleSuggestionSelect", "blur", "id", "encodeURIComponent", "handleSubmit", "handleClickOutside", "event", "contains", "document", "addEventListener", "removeEventListener", "ref", "children", "onSubmit", "type", "onChange", "onKeyDown", "onFocus", "autoComplete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "onSelect", "onClose", "formatPrice", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/search/SearchInput.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { usePrice } from '../../hooks/usePrice';\nimport { getProducts } from '../../services/products';\nimport SearchSuggestions from './SearchSuggestions';\n\nconst SearchInput = ({ className = '', placeholder = '' }) => {\n    const [query, setQuery] = useState('');\n    const [suggestions, setSuggestions] = useState([]);\n    const [isLoading, setIsLoading] = useState(false);\n    const [showSuggestions, setShowSuggestions] = useState(false);\n    const [selectedIndex, setSelectedIndex] = useState(-1);\n    const [searchCache, setSearchCache] = useState(new Map());\n    \n    const searchRef = useRef(null);\n    const inputRef = useRef(null);\n    const debounceRef = useRef(null);\n    const navigate = useNavigate();\n    \n    const { t } = useLanguage();\n    const { formatSinglePrice } = usePrice();\n\n    // Debounced search function\n    const debouncedSearch = useCallback((searchQuery) => {\n        if (debounceRef.current) {\n            clearTimeout(debounceRef.current);\n        }\n        \n        debounceRef.current = setTimeout(() => {\n            performSearch(searchQuery);\n        }, 300);\n    }, []);\n\n    // Perform search with caching\n    const performSearch = async (searchQuery) => {\n        if (searchQuery.length < 2) {\n            setSuggestions([]);\n            setShowSuggestions(false);\n            return;\n        }\n\n        // Check cache first\n        const cacheKey = searchQuery.toLowerCase();\n        if (searchCache.has(cacheKey)) {\n            const cachedResults = searchCache.get(cacheKey);\n            setSuggestions(cachedResults);\n            setShowSuggestions(true);\n            return;\n        }\n\n        setIsLoading(true);\n        \n        try {\n            const products = await getProducts();\n            const filteredProducts = filterProducts(products, searchQuery);\n            \n            // Cache the results\n            setSearchCache(prev => {\n                const newCache = new Map(prev);\n                newCache.set(cacheKey, filteredProducts);\n                \n                // Limit cache size to 50 entries\n                if (newCache.size > 50) {\n                    const firstKey = newCache.keys().next().value;\n                    newCache.delete(firstKey);\n                }\n                \n                return newCache;\n            });\n            \n            setSuggestions(filteredProducts);\n            setShowSuggestions(true);\n        } catch (error) {\n            console.error('Search error:', error);\n            setSuggestions([]);\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    // Filter products based on search query\n    const filterProducts = (products, searchQuery) => {\n        const query = searchQuery.toLowerCase().trim();\n        \n        return products\n            .filter(product => {\n                const name = product.name.toLowerCase();\n                const category = product.categoryName.toLowerCase();\n                const description = product.description.toLowerCase();\n                \n                return name.includes(query) || \n                       category.includes(query) || \n                       description.includes(query);\n            })\n            .sort((a, b) => {\n                // Prioritize exact name matches\n                const aNameMatch = a.name.toLowerCase().startsWith(query);\n                const bNameMatch = b.name.toLowerCase().startsWith(query);\n                \n                if (aNameMatch && !bNameMatch) return -1;\n                if (!aNameMatch && bNameMatch) return 1;\n                \n                // Then prioritize category matches\n                const aCategoryMatch = a.categoryName.toLowerCase().includes(query);\n                const bCategoryMatch = b.categoryName.toLowerCase().includes(query);\n                \n                if (aCategoryMatch && !bCategoryMatch) return -1;\n                if (!aCategoryMatch && bCategoryMatch) return 1;\n                \n                // Finally sort by name\n                return a.name.localeCompare(b.name);\n            })\n            .slice(0, 6); // Limit to 6 suggestions\n    };\n\n    // Handle input change\n    const handleInputChange = (e) => {\n        const value = e.target.value;\n        setQuery(value);\n        setSelectedIndex(-1);\n        \n        if (value.trim()) {\n            debouncedSearch(value);\n        } else {\n            setSuggestions([]);\n            setShowSuggestions(false);\n        }\n    };\n\n    // Handle keyboard navigation\n    const handleKeyDown = (e) => {\n        if (!showSuggestions || suggestions.length === 0) {\n            if (e.key === 'Enter') {\n                handleSearch();\n            }\n            return;\n        }\n\n        switch (e.key) {\n            case 'ArrowDown':\n                e.preventDefault();\n                setSelectedIndex(prev => \n                    prev < suggestions.length - 1 ? prev + 1 : prev\n                );\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);\n                break;\n            case 'Enter':\n                e.preventDefault();\n                if (selectedIndex >= 0) {\n                    handleSuggestionSelect(suggestions[selectedIndex]);\n                } else {\n                    handleSearch();\n                }\n                break;\n            case 'Escape':\n                setShowSuggestions(false);\n                setSelectedIndex(-1);\n                inputRef.current?.blur();\n                break;\n            default:\n                break;\n        }\n    };\n\n    // Handle suggestion selection\n    const handleSuggestionSelect = (product) => {\n        setQuery(product.name);\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n        navigate(`/products/${product.id}`);\n    };\n\n    // Handle search submission\n    const handleSearch = () => {\n        if (query.trim()) {\n            setShowSuggestions(false);\n            navigate(`/products?search=${encodeURIComponent(query.trim())}`);\n        }\n    };\n\n    // Handle form submission\n    const handleSubmit = (e) => {\n        e.preventDefault();\n        handleSearch();\n    };\n\n    // Close suggestions when clicking outside\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (searchRef.current && !searchRef.current.contains(event.target)) {\n                setShowSuggestions(false);\n                setSelectedIndex(-1);\n            }\n        };\n\n        document.addEventListener('mousedown', handleClickOutside);\n        return () => {\n            document.removeEventListener('mousedown', handleClickOutside);\n        };\n    }, []);\n\n    // Cleanup debounce on unmount\n    useEffect(() => {\n        return () => {\n            if (debounceRef.current) {\n                clearTimeout(debounceRef.current);\n            }\n        };\n    }, []);\n\n    return (\n        <div className={`search-container ${className}`} ref={searchRef}>\n            <form onSubmit={handleSubmit} className=\"search-form\">\n                <div className=\"search-input-wrapper\">\n                    <input\n                        ref={inputRef}\n                        type=\"text\"\n                        value={query}\n                        onChange={handleInputChange}\n                        onKeyDown={handleKeyDown}\n                        onFocus={() => query.length >= 2 && setShowSuggestions(true)}\n                        placeholder={placeholder || t('searchProducts')}\n                        className=\"search-input\"\n                        autoComplete=\"off\"\n                    />\n                    <button\n                        type=\"submit\"\n                        className=\"search-button\"\n                        disabled={!query.trim()}\n                    >\n                        <svg\n                            width=\"20\"\n                            height=\"20\"\n                            viewBox=\"0 0 24 24\"\n                            fill=\"none\"\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                            <path\n                                d=\"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\"\n                                stroke=\"currentColor\"\n                                strokeWidth=\"2\"\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                            />\n                        </svg>\n                    </button>\n                    \n                    {isLoading && (\n                        <div className=\"search-loading\">\n                            <div className=\"loading-spinner\"></div>\n                        </div>\n                    )}\n                </div>\n            </form>\n\n            {showSuggestions && (\n                <SearchSuggestions\n                    suggestions={suggestions}\n                    selectedIndex={selectedIndex}\n                    onSelect={handleSuggestionSelect}\n                    onClose={() => setShowSuggestions(false)}\n                    formatPrice={formatSinglePrice}\n                    isLoading={isLoading}\n                />\n            )}\n        </div>\n    );\n};\n\nexport default SearchInput;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,WAAW,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,IAAI2B,GAAG,CAAC,CAAC,CAAC;EAEzD,MAAMC,SAAS,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4B,QAAQ,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM6B,WAAW,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM8B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAE4B;EAAE,CAAC,GAAG3B,WAAW,CAAC,CAAC;EAC3B,MAAM;IAAE4B;EAAkB,CAAC,GAAG3B,QAAQ,CAAC,CAAC;;EAExC;EACA,MAAM4B,eAAe,GAAG/B,WAAW,CAAEgC,WAAW,IAAK;IACjD,IAAIL,WAAW,CAACM,OAAO,EAAE;MACrBC,YAAY,CAACP,WAAW,CAACM,OAAO,CAAC;IACrC;IAEAN,WAAW,CAACM,OAAO,GAAGE,UAAU,CAAC,MAAM;MACnCC,aAAa,CAACJ,WAAW,CAAC;IAC9B,CAAC,EAAE,GAAG,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,aAAa,GAAG,MAAOJ,WAAW,IAAK;IACzC,IAAIA,WAAW,CAACK,MAAM,GAAG,CAAC,EAAE;MACxBtB,cAAc,CAAC,EAAE,CAAC;MAClBI,kBAAkB,CAAC,KAAK,CAAC;MACzB;IACJ;;IAEA;IACA,MAAMmB,QAAQ,GAAGN,WAAW,CAACO,WAAW,CAAC,CAAC;IAC1C,IAAIjB,WAAW,CAACkB,GAAG,CAACF,QAAQ,CAAC,EAAE;MAC3B,MAAMG,aAAa,GAAGnB,WAAW,CAACoB,GAAG,CAACJ,QAAQ,CAAC;MAC/CvB,cAAc,CAAC0B,aAAa,CAAC;MAC7BtB,kBAAkB,CAAC,IAAI,CAAC;MACxB;IACJ;IAEAF,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAM0B,QAAQ,GAAG,MAAMvC,WAAW,CAAC,CAAC;MACpC,MAAMwC,gBAAgB,GAAGC,cAAc,CAACF,QAAQ,EAAEX,WAAW,CAAC;;MAE9D;MACAT,cAAc,CAACuB,IAAI,IAAI;QACnB,MAAMC,QAAQ,GAAG,IAAIvB,GAAG,CAACsB,IAAI,CAAC;QAC9BC,QAAQ,CAACC,GAAG,CAACV,QAAQ,EAAEM,gBAAgB,CAAC;;QAExC;QACA,IAAIG,QAAQ,CAACE,IAAI,GAAG,EAAE,EAAE;UACpB,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK;UAC7CN,QAAQ,CAACO,MAAM,CAACJ,QAAQ,CAAC;QAC7B;QAEA,OAAOH,QAAQ;MACnB,CAAC,CAAC;MAEFhC,cAAc,CAAC6B,gBAAgB,CAAC;MAChCzB,kBAAkB,CAAC,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCxC,cAAc,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;;EAED;EACA,MAAM4B,cAAc,GAAGA,CAACF,QAAQ,EAAEX,WAAW,KAAK;IAC9C,MAAMpB,KAAK,GAAGoB,WAAW,CAACO,WAAW,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC;IAE9C,OAAOd,QAAQ,CACVe,MAAM,CAACC,OAAO,IAAI;MACf,MAAMC,IAAI,GAAGD,OAAO,CAACC,IAAI,CAACrB,WAAW,CAAC,CAAC;MACvC,MAAMsB,QAAQ,GAAGF,OAAO,CAACG,YAAY,CAACvB,WAAW,CAAC,CAAC;MACnD,MAAMwB,WAAW,GAAGJ,OAAO,CAACI,WAAW,CAACxB,WAAW,CAAC,CAAC;MAErD,OAAOqB,IAAI,CAACI,QAAQ,CAACpD,KAAK,CAAC,IACpBiD,QAAQ,CAACG,QAAQ,CAACpD,KAAK,CAAC,IACxBmD,WAAW,CAACC,QAAQ,CAACpD,KAAK,CAAC;IACtC,CAAC,CAAC,CACDqD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACZ;MACA,MAAMC,UAAU,GAAGF,CAAC,CAACN,IAAI,CAACrB,WAAW,CAAC,CAAC,CAAC8B,UAAU,CAACzD,KAAK,CAAC;MACzD,MAAM0D,UAAU,GAAGH,CAAC,CAACP,IAAI,CAACrB,WAAW,CAAC,CAAC,CAAC8B,UAAU,CAACzD,KAAK,CAAC;MAEzD,IAAIwD,UAAU,IAAI,CAACE,UAAU,EAAE,OAAO,CAAC,CAAC;MACxC,IAAI,CAACF,UAAU,IAAIE,UAAU,EAAE,OAAO,CAAC;;MAEvC;MACA,MAAMC,cAAc,GAAGL,CAAC,CAACJ,YAAY,CAACvB,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACpD,KAAK,CAAC;MACnE,MAAM4D,cAAc,GAAGL,CAAC,CAACL,YAAY,CAACvB,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACpD,KAAK,CAAC;MAEnE,IAAI2D,cAAc,IAAI,CAACC,cAAc,EAAE,OAAO,CAAC,CAAC;MAChD,IAAI,CAACD,cAAc,IAAIC,cAAc,EAAE,OAAO,CAAC;;MAE/C;MACA,OAAON,CAAC,CAACN,IAAI,CAACa,aAAa,CAACN,CAAC,CAACP,IAAI,CAAC;IACvC,CAAC,CAAC,CACDc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAMvB,KAAK,GAAGuB,CAAC,CAACC,MAAM,CAACxB,KAAK;IAC5BxC,QAAQ,CAACwC,KAAK,CAAC;IACfhC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAEpB,IAAIgC,KAAK,CAACI,IAAI,CAAC,CAAC,EAAE;MACd1B,eAAe,CAACsB,KAAK,CAAC;IAC1B,CAAC,MAAM;MACHtC,cAAc,CAAC,EAAE,CAAC;MAClBI,kBAAkB,CAAC,KAAK,CAAC;IAC7B;EACJ,CAAC;;EAED;EACA,MAAM2D,aAAa,GAAIF,CAAC,IAAK;IAAA,IAAAG,iBAAA;IACzB,IAAI,CAAC7D,eAAe,IAAIJ,WAAW,CAACuB,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAIuC,CAAC,CAACI,GAAG,KAAK,OAAO,EAAE;QACnBC,YAAY,CAAC,CAAC;MAClB;MACA;IACJ;IAEA,QAAQL,CAAC,CAACI,GAAG;MACT,KAAK,WAAW;QACZJ,CAAC,CAACM,cAAc,CAAC,CAAC;QAClB7D,gBAAgB,CAACyB,IAAI,IACjBA,IAAI,GAAGhC,WAAW,CAACuB,MAAM,GAAG,CAAC,GAAGS,IAAI,GAAG,CAAC,GAAGA,IAC/C,CAAC;QACD;MACJ,KAAK,SAAS;QACV8B,CAAC,CAACM,cAAc,CAAC,CAAC;QAClB7D,gBAAgB,CAACyB,IAAI,IAAIA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD;MACJ,KAAK,OAAO;QACR8B,CAAC,CAACM,cAAc,CAAC,CAAC;QAClB,IAAI9D,aAAa,IAAI,CAAC,EAAE;UACpB+D,sBAAsB,CAACrE,WAAW,CAACM,aAAa,CAAC,CAAC;QACtD,CAAC,MAAM;UACH6D,YAAY,CAAC,CAAC;QAClB;QACA;MACJ,KAAK,QAAQ;QACT9D,kBAAkB,CAAC,KAAK,CAAC;QACzBE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACpB,CAAA0D,iBAAA,GAAArD,QAAQ,CAACO,OAAO,cAAA8C,iBAAA,uBAAhBA,iBAAA,CAAkBK,IAAI,CAAC,CAAC;QACxB;MACJ;QACI;IACR;EACJ,CAAC;;EAED;EACA,MAAMD,sBAAsB,GAAIxB,OAAO,IAAK;IACxC9C,QAAQ,CAAC8C,OAAO,CAACC,IAAI,CAAC;IACtBzC,kBAAkB,CAAC,KAAK,CAAC;IACzBE,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpBO,QAAQ,CAAC,aAAa+B,OAAO,CAAC0B,EAAE,EAAE,CAAC;EACvC,CAAC;;EAED;EACA,MAAMJ,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIrE,KAAK,CAAC6C,IAAI,CAAC,CAAC,EAAE;MACdtC,kBAAkB,CAAC,KAAK,CAAC;MACzBS,QAAQ,CAAC,oBAAoB0D,kBAAkB,CAAC1E,KAAK,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACpE;EACJ,CAAC;;EAED;EACA,MAAM8B,YAAY,GAAIX,CAAC,IAAK;IACxBA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBD,YAAY,CAAC,CAAC;EAClB,CAAC;;EAED;EACAlF,SAAS,CAAC,MAAM;IACZ,MAAMyF,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIhE,SAAS,CAACQ,OAAO,IAAI,CAACR,SAAS,CAACQ,OAAO,CAACyD,QAAQ,CAACD,KAAK,CAACZ,MAAM,CAAC,EAAE;QAChE1D,kBAAkB,CAAC,KAAK,CAAC;QACzBE,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACxB;IACJ,CAAC;IAEDsE,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACTG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IACjE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzF,SAAS,CAAC,MAAM;IACZ,OAAO,MAAM;MACT,IAAI4B,WAAW,CAACM,OAAO,EAAE;QACrBC,YAAY,CAACP,WAAW,CAACM,OAAO,CAAC;MACrC;IACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,oBACI1B,OAAA;IAAKE,SAAS,EAAE,oBAAoBA,SAAS,EAAG;IAACqF,GAAG,EAAErE,SAAU;IAAAsE,QAAA,gBAC5DxF,OAAA;MAAMyF,QAAQ,EAAET,YAAa;MAAC9E,SAAS,EAAC,aAAa;MAAAsF,QAAA,eACjDxF,OAAA;QAAKE,SAAS,EAAC,sBAAsB;QAAAsF,QAAA,gBACjCxF,OAAA;UACIuF,GAAG,EAAEpE,QAAS;UACduE,IAAI,EAAC,MAAM;UACX5C,KAAK,EAAEzC,KAAM;UACbsF,QAAQ,EAAEvB,iBAAkB;UAC5BwB,SAAS,EAAErB,aAAc;UACzBsB,OAAO,EAAEA,CAAA,KAAMxF,KAAK,CAACyB,MAAM,IAAI,CAAC,IAAIlB,kBAAkB,CAAC,IAAI,CAAE;UAC7DT,WAAW,EAAEA,WAAW,IAAImB,CAAC,CAAC,gBAAgB,CAAE;UAChDpB,SAAS,EAAC,cAAc;UACxB4F,YAAY,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFlG,OAAA;UACI0F,IAAI,EAAC,QAAQ;UACbxF,SAAS,EAAC,eAAe;UACzBiG,QAAQ,EAAE,CAAC9F,KAAK,CAAC6C,IAAI,CAAC,CAAE;UAAAsC,QAAA,eAExBxF,OAAA;YACIoG,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,4BAA4B;YAAAhB,QAAA,eAElCxF,OAAA;cACIyG,CAAC,EAAC,4IAA4I;cAC9IC,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC,GAAG;cACfC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAERzF,SAAS,iBACNT,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAsF,QAAA,eAC3BxF,OAAA;YAAKE,SAAS,EAAC;UAAiB;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAENvF,eAAe,iBACZX,OAAA,CAACF,iBAAiB;MACdS,WAAW,EAAEA,WAAY;MACzBM,aAAa,EAAEA,aAAc;MAC7BiG,QAAQ,EAAElC,sBAAuB;MACjCmC,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAAC,KAAK,CAAE;MACzCoG,WAAW,EAAEzF,iBAAkB;MAC/Bd,SAAS,EAAEA;IAAU;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC9F,EAAA,CAxQIH,WAAW;EAAA,QAWIP,WAAW,EAEdC,WAAW,EACKC,QAAQ;AAAA;AAAAqH,EAAA,GAdpChH,WAAW;AA0QjB,eAAeA,WAAW;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}