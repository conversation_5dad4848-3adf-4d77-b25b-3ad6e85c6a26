{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\common\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport CartIcon from '../cart/CartIcon';\nimport Logo from './Logo';\nimport CurrencyLanguageSelector from './CurrencyLanguageSelector';\nimport SearchInput from '../search/SearchInput';\nimport '../../styles/components.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const {\n    t\n  } = useLanguage();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n    setIsMenuOpen(false);\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"special-offer-banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"offer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-icon\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-text\",\n            children: t('specialOffer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-details\",\n            children: t('offerText')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"offer-shop-btn\",\n            children: t('shopNow')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"offer-close\",\n            onClick: () => document.querySelector('.special-offer-banner').style.display = 'none',\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"top-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"top-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"14\",\n                height: \"14\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                style: {\n                  marginRight: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"22,6 12,13 2,6\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 33\n              }, this), \"<EMAIL>\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"14\",\n                height: \"14\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                style: {\n                  marginRight: '0.5rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 33\n              }, this), \"(02) 413-6682\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"location-info\",\n            children: \"#1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(CurrencyLanguageSelector, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"main-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-search\",\n            children: /*#__PURE__*/_jsxDEV(SearchInput, {\n              className: \"header-search-input\",\n              placeholder: t('searchProducts')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-logo\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"logo\",\n              children: /*#__PURE__*/_jsxDEV(Logo, {\n                size: \"default\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-tagline\",\n              children: \"EXCELLENCE IN DESIGN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-actions\",\n            children: [isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-menu\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"action-btn user-btn\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"user-greeting\",\n                children: [\"Hello, \", user === null || user === void 0 ? void 0 : user.firstName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"logout-btn\",\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"action-btn user-btn\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(CartIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"action-btn contact-btn\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"22,6 12,13 2,6\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"mobile-menu-toggle\",\n              onClick: toggleMenu,\n              \"aria-label\": \"Toggle menu\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navigation-bar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"main-navigation\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-link\",\n            children: t('home')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"nav-link\",\n            children: t('products')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/configurator\",\n            className: \"nav-link\",\n            children: t('customFurniture')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/gallery\",\n            className: \"nav-link\",\n            children: t('gallery')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"nav-link\",\n            children: t('about')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"nav-link\",\n            children: t('contact')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/payment\",\n            className: \"nav-link\",\n            children: t('payments')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: `mobile-nav ${isMenuOpen ? 'open' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('home')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/products\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('products')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/configurator\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('customFurniture')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/gallery\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('gallery')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/about\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('about')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/contact\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('contact')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/payment\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('payments')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"mobile-nav-link login\",\n        onClick: () => setIsMenuOpen(false),\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n};\n_s(Header, \"nwOr1waml6pnUxh8Xn3Ye/rKzp8=\", false, function () {\n  return [useAuth, useLanguage, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "useLanguage", "CartIcon", "Logo", "CurrencyLanguageSelector", "SearchInput", "jsxDEV", "_jsxDEV", "Header", "_s", "user", "logout", "isAuthenticated", "t", "isMenuOpen", "setIsMenuOpen", "navigate", "handleLogout", "toggleMenu", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "document", "querySelector", "style", "display", "width", "height", "viewBox", "fill", "xmlns", "marginRight", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "points", "placeholder", "to", "size", "firstName", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/common/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport CartIcon from '../cart/CartIcon';\nimport Logo from './Logo';\nimport CurrencyLanguageSelector from './CurrencyLanguageSelector';\nimport SearchInput from '../search/SearchInput';\nimport '../../styles/components.css';\n\nconst Header = () => {\n    const { user, logout, isAuthenticated } = useAuth();\n    const { t } = useLanguage();\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const navigate = useNavigate();\n\n    const handleLogout = () => {\n        logout();\n        navigate('/');\n        setIsMenuOpen(false);\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n    };\n\n    return (\n        <header className=\"header\">\n            {/* Special Offer Banner */}\n            <div className=\"special-offer-banner\">\n                <div className=\"container\">\n                    <div className=\"offer-content\">\n                        <span className=\"offer-icon\">⚡</span>\n                        <span className=\"offer-text\">{t('specialOffer')}</span>\n                        <span className=\"offer-details\">{t('offerText')}</span>\n                        <button className=\"offer-shop-btn\">{t('shopNow')}</button>\n                        <button className=\"offer-close\" onClick={() => document.querySelector('.special-offer-banner').style.display = 'none'}>×</button>\n                    </div>\n                </div>\n            </div>\n\n            {/* Top Header Info */}\n            <div className=\"top-header\">\n                <div className=\"container\">\n                    <div className=\"top-header-content\">\n                        <div className=\"contact-info\">\n                            <span className=\"contact-item\">\n                                <svg\n                                    width=\"14\"\n                                    height=\"14\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    style={{ marginRight: '0.5rem' }}\n                                >\n                                    <path\n                                        d=\"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                    <polyline\n                                        points=\"22,6 12,13 2,6\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                                <EMAIL>\n                            </span>\n                            <span className=\"contact-item\">\n                                <svg\n                                    width=\"14\"\n                                    height=\"14\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    style={{ marginRight: '0.5rem' }}\n                                >\n                                    <path\n                                        d=\"M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                                (02) 413-6682\n                            </span>\n                        </div>\n                        <div className=\"location-info\">\n                            #1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City\n                        </div>\n                        <CurrencyLanguageSelector />\n                    </div>\n                </div>\n            </div>\n\n            {/* Main Header */}\n            <div className=\"main-header\">\n                <div className=\"container\">\n                    <div className=\"main-header-content\">\n                        {/* Search */}\n                        <div className=\"header-search\">\n                            <SearchInput\n                                className=\"header-search-input\"\n                                placeholder={t('searchProducts')}\n                            />\n                        </div>\n\n                        {/* Centered Logo */}\n                        <div className=\"header-logo\">\n                            <Link to=\"/\" className=\"logo\">\n                                <Logo size=\"default\" />\n                            </Link>\n                            <div className=\"logo-tagline\">EXCELLENCE IN DESIGN</div>\n                        </div>\n\n                        {/* User Actions */}\n                        <div className=\"header-actions\">\n                            {/* User Account */}\n                            {isAuthenticated ? (\n                                <div className=\"user-menu\">\n                                    <button className=\"action-btn user-btn\">\n                                        <svg\n                                            width=\"20\"\n                                            height=\"20\"\n                                            viewBox=\"0 0 24 24\"\n                                            fill=\"none\"\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                        >\n                                            <path\n                                                d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\"\n                                                stroke=\"#F0B21B\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                        </svg>\n                                    </button>\n                                    <span className=\"user-greeting\">\n                                        Hello, {user?.firstName}\n                                    </span>\n                                    <button onClick={handleLogout} className=\"logout-btn\">\n                                        Logout\n                                    </button>\n                                </div>\n                            ) : (\n                                <Link to=\"/login\" className=\"action-btn user-btn\">\n                                    <svg\n                                        width=\"20\"\n                                        height=\"20\"\n                                        viewBox=\"0 0 24 24\"\n                                        fill=\"none\"\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                    >\n                                        <path\n                                            d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\"\n                                            stroke=\"#F0B21B\"\n                                            strokeWidth=\"2\"\n                                            strokeLinecap=\"round\"\n                                            strokeLinejoin=\"round\"\n                                        />\n                                    </svg>\n                                </Link>\n                            )}\n\n                            {/* Shopping Cart */}\n                            <CartIcon />\n\n                            {/* Contact */}\n                            <Link to=\"/contact\" className=\"action-btn contact-btn\">\n                                <svg\n                                    width=\"20\"\n                                    height=\"20\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                >\n                                    <path\n                                        d=\"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                    <polyline\n                                        points=\"22,6 12,13 2,6\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                            </Link>\n\n                            {/* Mobile Menu Toggle */}\n                            <button\n                                className=\"mobile-menu-toggle\"\n                                onClick={toggleMenu}\n                                aria-label=\"Toggle menu\"\n                            >\n                                <span></span>\n                                <span></span>\n                                <span></span>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Navigation Bar */}\n            <div className=\"navigation-bar\">\n                <div className=\"container\">\n                    <nav className=\"main-navigation\">\n                        <Link to=\"/\" className=\"nav-link\">{t('home')}</Link>\n                        <Link to=\"/products\" className=\"nav-link\">{t('products')}</Link>\n                        <Link to=\"/configurator\" className=\"nav-link\">{t('customFurniture')}</Link>\n                        <Link to=\"/gallery\" className=\"nav-link\">{t('gallery')}</Link>\n                        <Link to=\"/about\" className=\"nav-link\">{t('about')}</Link>\n                        <Link to=\"/contact\" className=\"nav-link\">{t('contact')}</Link>\n                        <Link to=\"/payment\" className=\"nav-link\">{t('payments')}</Link>\n                    </nav>\n                </div>\n            </div>\n\n            {/* Mobile Navigation */}\n            <nav className={`mobile-nav ${isMenuOpen ? 'open' : ''}`}>\n                <Link\n                    to=\"/\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('home')}\n                </Link>\n                <Link\n                    to=\"/products\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('products')}\n                </Link>\n                <Link\n                    to=\"/configurator\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('customFurniture')}\n                </Link>\n                <Link\n                    to=\"/gallery\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('gallery')}\n                </Link>\n                <Link\n                    to=\"/about\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('about')}\n                </Link>\n                <Link\n                    to=\"/contact\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('contact')}\n                </Link>\n                <Link\n                    to=\"/payment\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('payments')}\n                </Link>\n\n                {!isAuthenticated && (\n                    <Link\n                        to=\"/login\"\n                        className=\"mobile-nav-link login\"\n                        onClick={() => setIsMenuOpen(false)}\n                    >\n                        Login\n                    </Link>\n                )}\n            </nav>\n        </header>\n    );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEa;EAAE,CAAC,GAAGZ,WAAW,CAAC,CAAC;EAC3B,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACvBN,MAAM,CAAC,CAAC;IACRK,QAAQ,CAAC,GAAG,CAAC;IACbD,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACrBH,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,oBACIP,OAAA;IAAQY,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAEtBb,OAAA;MAAKY,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACjCb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBb,OAAA;UAAKY,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1Bb,OAAA;YAAMY,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrCjB,OAAA;YAAMY,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEP,CAAC,CAAC,cAAc;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDjB,OAAA;YAAMY,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEP,CAAC,CAAC,WAAW;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDjB,OAAA;YAAQY,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEP,CAAC,CAAC,SAAS;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC1DjB,OAAA;YAAQY,SAAS,EAAC,aAAa;YAACM,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,aAAa,CAAC,uBAAuB,CAAC,CAACC,KAAK,CAACC,OAAO,GAAG,MAAO;YAAAT,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjB,OAAA;MAAKY,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvBb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBb,OAAA;UAAKY,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/Bb,OAAA;YAAKY,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBb,OAAA;cAAMY,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1Bb,OAAA;gBACIuB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAClCN,KAAK,EAAE;kBAAEO,WAAW,EAAE;gBAAS,CAAE;gBAAAf,QAAA,gBAEjCb,OAAA;kBACI6B,CAAC,EAAC,6FAA6F;kBAC/FC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFjB,OAAA;kBACIkC,MAAM,EAAC,gBAAgB;kBACvBJ,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,+BAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPjB,OAAA;cAAMY,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1Bb,OAAA;gBACIuB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAClCN,KAAK,EAAE;kBAAEO,WAAW,EAAE;gBAAS,CAAE;gBAAAf,QAAA,eAEjCb,OAAA;kBACI6B,CAAC,EAAC,+hCAA+hC;kBACjiCC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,iBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNjB,OAAA;YAAKY,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjB,OAAA,CAACH,wBAAwB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjB,OAAA;MAAKY,SAAS,EAAC,aAAa;MAAAC,QAAA,eACxBb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBb,OAAA;UAAKY,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAEhCb,OAAA;YAAKY,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1Bb,OAAA,CAACF,WAAW;cACRc,SAAS,EAAC,qBAAqB;cAC/BuB,WAAW,EAAE7B,CAAC,CAAC,gBAAgB;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNjB,OAAA;YAAKY,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBb,OAAA,CAACT,IAAI;cAAC6C,EAAE,EAAC,GAAG;cAACxB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACzBb,OAAA,CAACJ,IAAI;gBAACyC,IAAI,EAAC;cAAS;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACPjB,OAAA;cAAKY,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAGNjB,OAAA;YAAKY,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAE1BR,eAAe,gBACZL,OAAA;cAAKY,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBb,OAAA;gBAAQY,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eACnCb,OAAA;kBACIuB,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,eAElCb,OAAA;oBACI6B,CAAC,EAAC,oRAAoR;oBACtRC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACTjB,OAAA;gBAAMY,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,SACrB,EAACV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,SAAS;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACPjB,OAAA;gBAAQkB,OAAO,EAAER,YAAa;gBAACE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAEtD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,gBAENjB,OAAA,CAACT,IAAI;cAAC6C,EAAE,EAAC,QAAQ;cAACxB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAC7Cb,OAAA;gBACIuB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAAAd,QAAA,eAElCb,OAAA;kBACI6B,CAAC,EAAC,oRAAoR;kBACtRC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACT,eAGDjB,OAAA,CAACL,QAAQ;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGZjB,OAAA,CAACT,IAAI;cAAC6C,EAAE,EAAC,UAAU;cAACxB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eAClDb,OAAA;gBACIuB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAAAd,QAAA,gBAElCb,OAAA;kBACI6B,CAAC,EAAC,6FAA6F;kBAC/FC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFjB,OAAA;kBACIkC,MAAM,EAAC,gBAAgB;kBACvBJ,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGPjB,OAAA;cACIY,SAAS,EAAC,oBAAoB;cAC9BM,OAAO,EAAEP,UAAW;cACpB,cAAW,aAAa;cAAAE,QAAA,gBAExBb,OAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjB,OAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjB,OAAA;MAAKY,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3Bb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBb,OAAA;UAAKY,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5Bb,OAAA,CAACT,IAAI;YAAC6C,EAAE,EAAC,GAAG;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEP,CAAC,CAAC,MAAM;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDjB,OAAA,CAACT,IAAI;YAAC6C,EAAE,EAAC,WAAW;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEP,CAAC,CAAC,UAAU;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChEjB,OAAA,CAACT,IAAI;YAAC6C,EAAE,EAAC,eAAe;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEP,CAAC,CAAC,iBAAiB;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3EjB,OAAA,CAACT,IAAI;YAAC6C,EAAE,EAAC,UAAU;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEP,CAAC,CAAC,SAAS;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DjB,OAAA,CAACT,IAAI;YAAC6C,EAAE,EAAC,QAAQ;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEP,CAAC,CAAC,OAAO;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1DjB,OAAA,CAACT,IAAI;YAAC6C,EAAE,EAAC,UAAU;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEP,CAAC,CAAC,SAAS;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DjB,OAAA,CAACT,IAAI;YAAC6C,EAAE,EAAC,UAAU;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEP,CAAC,CAAC,UAAU;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjB,OAAA;MAAKY,SAAS,EAAE,cAAcL,UAAU,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAM,QAAA,gBACrDb,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,GAAG;QACNxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EAEnCP,CAAC,CAAC,MAAM;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACPjB,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,WAAW;QACdxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EAEnCP,CAAC,CAAC,UAAU;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACPjB,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,eAAe;QAClBxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EAEnCP,CAAC,CAAC,iBAAiB;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACPjB,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,UAAU;QACbxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EAEnCP,CAAC,CAAC,SAAS;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACPjB,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,QAAQ;QACXxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EAEnCP,CAAC,CAAC,OAAO;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACPjB,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,UAAU;QACbxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EAEnCP,CAAC,CAAC,SAAS;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACPjB,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,UAAU;QACbxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EAEnCP,CAAC,CAAC,UAAU;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,EAEN,CAACZ,eAAe,iBACbL,OAAA,CAACT,IAAI;QACD6C,EAAE,EAAC,QAAQ;QACXxB,SAAS,EAAC,uBAAuB;QACjCM,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,KAAK,CAAE;QAAAK,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEjB,CAAC;AAACf,EAAA,CA1RID,MAAM;EAAA,QACkCR,OAAO,EACnCC,WAAW,EAERF,WAAW;AAAA;AAAA+C,EAAA,GAJ1BtC,MAAM;AA4RZ,eAAeA,MAAM;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}