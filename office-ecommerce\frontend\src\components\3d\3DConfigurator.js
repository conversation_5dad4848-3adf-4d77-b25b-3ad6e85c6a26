import React, { useState, useRef, useEffect } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import '../../styles/configurator.css';

const Advanced3DConfigurator = ({ onBack, product }) => {
  // 3D Scene refs
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const productRef = useRef(null);
  const animationIdRef = useRef(null);
  const cameraRef = useRef(null);

  // Determine product type
  const getProductType = () => {
    if (!product) return 'table';
    const name = product.name.toLowerCase();
    if (name.includes('chair')) return 'chair';
    if (name.includes('cabinet') || name.includes('storage')) return 'cabinet';
    if (name.includes('shelf')) return 'shelf';
    if (name.includes('workstation')) return 'workstation';
    return 'table'; // default
  };

  const productType = getProductType();

  // State for configuration
  const [dimensions, setDimensions] = useState({
    width: productType === 'chair' ? 60 : 280,
    depth: productType === 'chair' ? 60 : 140,
    height: productType === 'chair' ? 80 : 75
  });

  const [colors, setColors] = useState({
    primary: '#6B7280',
    secondary: '#374151',
    accent: '#FFFFFF'
  });

  const [material, setMaterial] = useState(productType === 'chair' ? 'mesh' : 'wood');
  const [quantity, setQuantity] = useState(1);

  // Model loading state
  const [isModelLoaded, setIsModelLoaded] = useState(false);
  const [modelError, setModelError] = useState('');

  // Store original model dimensions for scaling calculations
  const originalModelDimensionsRef = useRef(null);
  const baseScaleRef = useRef(1);

  // Scaling notification state
  const [showScalingNotification, setShowScalingNotification] = useState(false);
  const scalingTimeoutRef = useRef(null);

  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      setIsMobile(mobile);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Enhanced pricing logic
  const getBasePrice = () => {
    const basePrices = {
      table: 500,
      chair: 200,
      cabinet: 800,
      shelf: 300,
      workstation: 1200
    };
    return basePrices[productType] || 500;
  };

  const calculatePrice = () => {
    let price = getBasePrice();

    // Size multiplier
    const sizeMultiplier = (dimensions.width * dimensions.depth * dimensions.height) / 100000;
    price *= Math.max(0.5, Math.min(2.0, sizeMultiplier));

    // Material multiplier
    const materialMultipliers = {
      wood: 1.0,
      metal: 1.2,
      glass: 1.5,
      plastic: 0.8,
      leather: 1.8,
      mesh: 1.1,
      fabric: 0.9,
      vinyl: 1.0
    };
    price *= materialMultipliers[material] || 1.0;

    return price * quantity;
  };

  const getCurrentPrice = () => calculatePrice();

  // GLB Model Loading
  const loadGLBModel = async (scene, modelPath) => {
    const loader = new GLTFLoader();

    try {
      setModelError('');
      const gltf = await new Promise((resolve, reject) => {
        loader.load(
          modelPath,
          (gltf) => resolve(gltf),
          (progress) => {
            console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
          },
          (error) => reject(error)
        );
      });

      const model = gltf.scene;

      // Get model bounds and center it
      const box = new THREE.Box3().setFromObject(model);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());

      // Store original model dimensions for real-time scaling
      originalModelDimensionsRef.current = {
        width: size.x,
        height: size.y,
        depth: size.z
      };

      // Center the model
      model.position.sub(center);

      // Scale model to reasonable size and store base scale
      const maxDimension = Math.max(size.x, size.y, size.z);
      if (maxDimension > 3) {
        const scale = 3 / maxDimension;
        model.scale.setScalar(scale);
        baseScaleRef.current = scale;
      } else {
        baseScaleRef.current = 1;
      }

      // Apply initial dimension-based scaling for chairs
      if (productType === 'chair') {
        updateModelDimensions(model);
      }

      // Enable shadows for all meshes
      model.traverse((child) => {
        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;

          // Ensure materials are properly configured
          if (child.material) {
            child.material.needsUpdate = true;
          }
        }
      });

      productRef.current = model;
      scene.add(model);
      setIsModelLoaded(true);

      return model;
    } catch (error) {
      console.error('Error loading GLB model:', error);
      setModelError('Failed to load 3D model. Using default geometry.');
      setIsModelLoaded(false);

      // Fallback to geometric shapes
      createProduct(scene);
      return null;
    }
  };

  // Camera control state - moved outside useEffect for global access
  const cameraControlsRef = useRef({
    targetRotationX: 0,
    targetRotationY: 0,
    currentRotationX: 0,
    currentRotationY: 0,
    targetDistance: 6,
    currentDistance: 6,
    isAnimating: false
  });

  // View control functions
  const resetView = (viewType) => {
    if (!cameraRef.current) return;

    const controls = cameraControlsRef.current;
    controls.isAnimating = true;

    switch (viewType) {
      case 'front':
        controls.targetRotationX = 0;
        controls.targetRotationY = 0;
        controls.targetDistance = 6;
        break;
      case 'side':
        controls.targetRotationX = 0;
        controls.targetRotationY = Math.PI / 2;
        controls.targetDistance = 6;
        break;
      case 'top':
        controls.targetRotationX = Math.PI / 2;
        controls.targetRotationY = 0;
        controls.targetDistance = 8;
        break;
      case 'iso':
      default:
        controls.targetRotationX = 0.3;
        controls.targetRotationY = 0.8;
        controls.targetDistance = 6;
        break;
    }
  };

  const adjustZoom = (direction) => {
    if (!cameraRef.current || !cameraControlsRef.current) return;

    const controls = cameraControlsRef.current;
    const zoomAmount = direction * 1.5;
    const newDistance = controls.targetDistance + zoomAmount;

    // Smooth zoom with bounds checking
    controls.targetDistance = Math.max(2.5, Math.min(15, newDistance));

    // Add slight animation flag for smoother zoom
    controls.isAnimating = true;
    setTimeout(() => {
      if (controls) controls.isAnimating = false;
    }, 500);
  };

  // Create 3D product functions
  const createTableGeometry = (group) => {
    // Table top
    const topGeometry = new THREE.BoxGeometry(
      dimensions.width / 100,
      0.08,
      dimensions.depth / 100
    );
    const topMaterial = new THREE.MeshStandardMaterial({
      color: colors.primary,
      roughness: material === 'glass' ? 0.1 : 0.7,
      metalness: material === 'metal' ? 0.8 : 0.0,
      transparent: material === 'glass',
      opacity: material === 'glass' ? 0.8 : 1.0
    });

    const tableTop = new THREE.Mesh(topGeometry, topMaterial);
    tableTop.position.y = dimensions.height / 100 - 0.04;
    if (!isMobile) {
      tableTop.castShadow = true;
      tableTop.receiveShadow = true;
    }
    group.add(tableTop);

    // Legs with mobile optimization
    const legSegments = isMobile ? 8 : 12;
    const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, dimensions.height / 100 - 0.08, legSegments);
    const legMaterial = new THREE.MeshStandardMaterial({
      color: colors.primary,
      roughness: 0.7,
      metalness: material === 'metal' ? 0.8 : 0.0
    });

    const legPositions = [
      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],
      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],
      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1],
      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1]
    ];

    legPositions.forEach(pos => {
      const leg = new THREE.Mesh(legGeometry, legMaterial);
      leg.position.set(pos[0], pos[1], pos[2]);
      if (!isMobile) {
        leg.castShadow = true;
      }
      group.add(leg);
    });
  };

  const createChairGeometry = (group) => {
    // Seat
    const seatGeometry = new THREE.BoxGeometry(
      dimensions.width / 100,
      0.06,
      dimensions.depth / 100
    );
    const seatMaterial = new THREE.MeshStandardMaterial({
      color: colors.primary,
      roughness: 0.7
    });
    const seat = new THREE.Mesh(seatGeometry, seatMaterial);
    seat.position.y = 0.4;
    if (!isMobile) {
      seat.castShadow = true;
    }
    group.add(seat);

    // Backrest
    const backGeometry = new THREE.BoxGeometry(
      dimensions.width / 100,
      0.02,
      dimensions.height / 150
    );
    const backMaterial = new THREE.MeshStandardMaterial({
      color: colors.primary,
      roughness: 0.7
    });
    const backrest = new THREE.Mesh(backGeometry, backMaterial);
    backrest.position.set(0, 0.6, -dimensions.depth / 200 + 0.01);
    if (!isMobile) {
      backrest.castShadow = true;
    }
    group.add(backrest);

    // Base with mobile optimization
    const baseSegments = isMobile ? 5 : 8;
    const baseGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.04, baseSegments);
    const baseMaterial = new THREE.MeshStandardMaterial({
      color: colors.primary,
      roughness: 0.7
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = 0.02;
    if (!isMobile) {
      base.castShadow = true;
    }
    group.add(base);
  };

  const createProduct = async (scene) => {
    // Check if product has a 3D model file
    if (product && product.model3D) {
      console.log('Loading GLB model:', product.model3D);
      const model = await loadGLBModel(scene, product.model3D);
      if (model) {
        return; // Successfully loaded GLB model
      }
    }

    // Fallback to geometric shapes if no GLB model or loading failed
    const productGroup = new THREE.Group();

    switch (productType) {
      case 'table':
        createTableGeometry(productGroup);
        break;
      case 'chair':
        createChairGeometry(productGroup);
        break;
      default:
        createTableGeometry(productGroup);
    }

    productRef.current = productGroup;
    scene.add(productGroup);
  };

  // Update model dimensions in real-time for GLB models
  const updateModelDimensions = (model = productRef.current) => {
    if (!model || !originalModelDimensionsRef.current || productType !== 'chair') return;

    const baseScale = baseScaleRef.current;

    // Show scaling notification
    setShowScalingNotification(true);

    // Clear existing timeout
    if (scalingTimeoutRef.current) {
      clearTimeout(scalingTimeoutRef.current);
    }

    // Hide notification after 2 seconds
    scalingTimeoutRef.current = setTimeout(() => {
      setShowScalingNotification(false);
    }, 2000);

    // Calculate scale factors based on dimension changes
    // Default chair dimensions (in cm) for reference
    const defaultDimensions = {
      width: 60,
      height: 80,
      depth: 60
    };

    // Calculate scale factors for each dimension
    const scaleX = (dimensions.width / defaultDimensions.width) * baseScale;
    const scaleY = (dimensions.height / defaultDimensions.height) * baseScale;
    const scaleZ = (dimensions.depth / defaultDimensions.depth) * baseScale;

    // Apply non-uniform scaling to maintain realistic proportions
    // For chairs: width affects seat width, height affects overall height, depth affects seat depth
    model.scale.set(scaleX, scaleY, scaleZ);

    // Ensure the model stays centered after scaling
    const box = new THREE.Box3().setFromObject(model);
    const center = box.getCenter(new THREE.Vector3());
    model.position.sub(center);
  };

  // Update product materials and geometry
  const updateProduct = () => {
    if (!productRef.current || !sceneRef.current) return;

    // For GLB models with real-time dimension updates
    if (isModelLoaded && productType === 'chair') {
      updateModelDimensions();
      return;
    }

    // For procedural geometry, recreate the model
    sceneRef.current.remove(productRef.current);
    createProduct(sceneRef.current);
  };

  // 3D Scene Setup
  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf5f5f5); // Gray background
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(4, 3, 4);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // Renderer setup with mobile optimizations
    const renderer = new THREE.WebGLRenderer({
      antialias: !isMobile,
      alpha: true,
      powerPreference: isMobile ? "low-power" : "high-performance"
    });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, isMobile ? 1.5 : 2));
    renderer.setClearColor(0xf5f5f5, 1.0);
    renderer.shadowMap.enabled = !isMobile;
    if (!isMobile) {
      renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Lighting System with mobile optimizations
    const ambientLight = new THREE.AmbientLight(0x404040, isMobile ? 0.8 : 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, isMobile ? 0.8 : 1.0);
    directionalLight.position.set(5, 5, 5);
    if (!isMobile) {
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 1024;
      directionalLight.shadow.mapSize.height = 1024;
    }
    scene.add(directionalLight);

    // Mouse interaction variables
    let isMouseDown = false;
    let mouseX = 0;
    let mouseY = 0;

    // Get camera controls from ref
    const controls = cameraControlsRef.current;

    // Mouse event handlers
    const onMouseDown = (event) => {
      isMouseDown = true;
      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const onMouseMove = (event) => {
      if (!isMouseDown) return;
      const deltaX = event.clientX - mouseX;
      const deltaY = event.clientY - mouseY;

      // Increased sensitivity for better responsiveness
      controls.targetRotationY += deltaX * 0.015;
      controls.targetRotationX += deltaY * 0.015;

      controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));

      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const onMouseUp = () => {
      isMouseDown = false;
    };

    const onWheel = (event) => {
      event.preventDefault();
      const zoomSpeed = isMobile ? 0.008 : 0.01;
      controls.targetDistance += event.deltaY * zoomSpeed;
      controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));
    };

    // Enhanced touch event handlers for mobile
    let lastTouchDistance = 0;
    let initialTouchDistance = 0;

    const getTouchDistance = (touches) => {
      if (touches.length < 2) return 0;
      const dx = touches[0].clientX - touches[1].clientX;
      const dy = touches[0].clientY - touches[1].clientY;
      return Math.sqrt(dx * dx + dy * dy);
    };

    const onTouchStart = (event) => {
      if (event.touches.length === 1) {
        isMouseDown = true;
        mouseX = event.touches[0].clientX;
        mouseY = event.touches[0].clientY;
      } else if (event.touches.length === 2) {
        isMouseDown = false;
        initialTouchDistance = getTouchDistance(event.touches);
        lastTouchDistance = initialTouchDistance;
      }
    };

    const onTouchMove = (event) => {
      event.preventDefault();

      if (event.touches.length === 1 && isMouseDown) {
        // Single finger rotation with increased sensitivity
        const deltaX = event.touches[0].clientX - mouseX;
        const deltaY = event.touches[0].clientY - mouseY;

        controls.targetRotationY += deltaX * 0.012;
        controls.targetRotationX += deltaY * 0.012;

        controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));

        mouseX = event.touches[0].clientX;
        mouseY = event.touches[0].clientY;
      } else if (event.touches.length === 2) {
        // Two finger pinch zoom
        const currentDistance = getTouchDistance(event.touches);
        const deltaDistance = currentDistance - lastTouchDistance;

        if (Math.abs(deltaDistance) > 3) { // Increased threshold for smoother zoom
          controls.targetDistance -= deltaDistance * 0.015;
          controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));
          lastTouchDistance = currentDistance;
        }
      }
    };

    const onTouchEnd = (event) => {
      if (event.touches.length === 0) {
        isMouseDown = false;
      } else if (event.touches.length === 1) {
        isMouseDown = true;
        mouseX = event.touches[0].clientX;
        mouseY = event.touches[0].clientY;
      }
    };

    // Add event listeners to both canvas and document for better coverage
    const canvas = renderer.domElement;

    // Mouse events
    canvas.addEventListener('mousedown', onMouseDown);
    document.addEventListener('mousemove', onMouseMove); // Global mouse move for better tracking
    document.addEventListener('mouseup', onMouseUp); // Global mouse up
    canvas.addEventListener('wheel', onWheel, { passive: false });

    // Touch events with better gesture handling
    canvas.addEventListener('touchstart', onTouchStart, { passive: false });
    canvas.addEventListener('touchmove', onTouchMove, { passive: false });
    canvas.addEventListener('touchend', onTouchEnd, { passive: false });

    // Prevent context menu on long press
    canvas.addEventListener('contextmenu', (e) => e.preventDefault());

    // Prevent default touch behaviors that might interfere
    canvas.style.touchAction = 'none';
    canvas.style.userSelect = 'none';

    // Enhanced camera update function with smooth interpolation
    const updateCamera = () => {
      const lerpFactor = controls.isAnimating ? 0.15 : 0.08; // Faster when animating to presets

      // Smooth interpolation
      controls.currentRotationX += (controls.targetRotationX - controls.currentRotationX) * lerpFactor;
      controls.currentRotationY += (controls.targetRotationY - controls.currentRotationY) * lerpFactor;
      controls.currentDistance += (controls.targetDistance - controls.currentDistance) * lerpFactor;

      // Calculate spherical coordinates for camera position
      const x = Math.sin(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;
      const y = Math.sin(controls.currentRotationX) * controls.currentDistance + 2;
      const z = Math.cos(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;

      camera.position.set(x, y, z);
      camera.lookAt(0, 0, 0);

      // Stop animation flag when close enough to target
      if (controls.isAnimating) {
        const rotXDiff = Math.abs(controls.targetRotationX - controls.currentRotationX);
        const rotYDiff = Math.abs(controls.targetRotationY - controls.currentRotationY);
        const distDiff = Math.abs(controls.targetDistance - controls.currentDistance);

        if (rotXDiff < 0.01 && rotYDiff < 0.01 && distDiff < 0.1) {
          controls.isAnimating = false;
        }
      }
    };

    // Initialize camera controls with default isometric view
    controls.targetRotationX = 0.3;
    controls.targetRotationY = 0.8;
    controls.targetDistance = 6;
    controls.currentRotationX = 0.3;
    controls.currentRotationY = 0.8;
    controls.currentDistance = 6;

    // Create initial product
    createProduct(scene);

    // Animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);
      updateCamera();
      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!mountRef.current) return;
      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);

      // Remove canvas event listeners
      const canvas = renderer.domElement;
      canvas.removeEventListener('mousedown', onMouseDown);
      canvas.removeEventListener('wheel', onWheel);
      canvas.removeEventListener('touchstart', onTouchStart);
      canvas.removeEventListener('touchmove', onTouchMove);
      canvas.removeEventListener('touchend', onTouchEnd);
      canvas.removeEventListener('contextmenu', (e) => e.preventDefault());

      // Remove global event listeners
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);

      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      if (scalingTimeoutRef.current) {
        clearTimeout(scalingTimeoutRef.current);
      }
      renderer.dispose();
    };
  }, [isMobile, dimensions, colors, material, productType]);

  // Update product when configuration changes
  useEffect(() => {
    if (sceneRef.current && productRef.current) {
      updateProduct();
    }
  }, [dimensions, colors, material]);

  // Real-time dimension updates for GLB models (chairs)
  useEffect(() => {
    if (isModelLoaded && productRef.current && productType === 'chair') {
      updateModelDimensions();
    }
  }, [dimensions.width, dimensions.height, dimensions.depth, isModelLoaded, productType]);

  const handleAddToCart = () => {
    const configuration = {
      productType,
      dimensions,
      colors,
      material,
      quantity,
      price: getCurrentPrice()
    };

    console.log('Adding to cart:', configuration);
    alert(`Added ${quantity} ${productType}(s) to cart for $${getCurrentPrice().toFixed(2)}`);
  };

  return (
    <div className="configurator-container">
      {/* Header */}
      <div className="configurator-header">
        <div className="container">
          <button onClick={onBack} className="back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Products
          </button>
          <h1>Advanced {productType.charAt(0).toUpperCase() + productType.slice(1)} Configurator</h1>
        </div>
      </div>

      {/* Main Configuration */}
      <div className="configurator-main">
        <div className="container">
          <div className="configurator-layout-horizontal">
            {/* Left Side - 3D Model Viewer */}
            <div className="viewer-panel">
              <div className="config-card viewer-card">
                <div className="card-header">
                  <div className="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#F0B21B"/>
                      <path d="M2 17L12 22L22 17" stroke="#F0B21B" strokeWidth="2"/>
                      <path d="M2 12L12 17L22 12" stroke="#F0B21B" strokeWidth="2"/>
                    </svg>
                  </div>
                  <div className="card-title">
                    <h4>3D Preview</h4>
                    <p>Interactive model of your configured {productType}</p>
                  </div>
                </div>
                <div className="model-viewer-container">
                  <div
                    className={`model-viewer ${productType === 'chair' && isModelLoaded ? 'real-time-active' : ''}`}
                    ref={mountRef}
                  ></div>
                  {/* Real-time scaling notification */}
                  {productType === 'chair' && isModelLoaded && (
                    <div className={`scaling-notification ${showScalingNotification ? 'show' : ''}`}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M3 17h18M3 7h18M7 3v18M17 3v18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4" fill="currentColor"/>
                      </svg>
                      Real-time scaling active
                    </div>
                  )}
                  <div className="viewer-controls-new">
                    <div className="view-presets-horizontal">
                      <button
                        className="preset-btn-new"
                        onClick={() => resetView('front')}
                        title="Front View"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <rect x="4" y="4" width="16" height="16" stroke="currentColor" strokeWidth="1.5" fill="none" rx="2"/>
                          <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
                        </svg>
                        <span>Front</span>
                      </button>
                      <button
                        className="preset-btn-new"
                        onClick={() => resetView('side')}
                        title="Side View"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M4 12h16M12 4v16" stroke="currentColor" strokeWidth="1.5"/>
                          <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
                        </svg>
                        <span>Side</span>
                      </button>
                      <button
                        className="preset-btn-new"
                        onClick={() => resetView('top')}
                        title="Top View"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M12 3L3 7L12 11L21 7L12 3Z" fill="currentColor"/>
                          <path d="M3 16L12 20L21 16" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                        </svg>
                        <span>Top</span>
                      </button>
                      <button
                        className="preset-btn-new"
                        onClick={() => resetView('iso')}
                        title="3D View"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M12 3L3 7L12 11L21 7L12 3Z" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                          <path d="M3 16L12 20L21 16" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                          <path d="M3 11L12 15L21 11" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                        </svg>
                        <span>3D</span>
                      </button>
                    </div>
                    <div className="zoom-controls-horizontal">
                      <button
                        className="zoom-btn-new"
                        onClick={() => adjustZoom(-1)}
                        title="Zoom Out"
                      >
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                          <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="1.5"/>
                          <path d="M8 11h6" stroke="currentColor" strokeWidth="1.5"/>
                          <path d="M21 21l-4.35-4.35" stroke="currentColor" strokeWidth="1.5"/>
                        </svg>
                      </button>
                      <button
                        className="zoom-btn-new"
                        onClick={() => adjustZoom(1)}
                        title="Zoom In"
                      >
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                          <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="1.5"/>
                          <path d="M11 8v6M8 11h6" stroke="currentColor" strokeWidth="1.5"/>
                          <path d="M21 21l-4.35-4.35" stroke="currentColor" strokeWidth="1.5"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div className="viewer-instructions">
                    <span>
                      {isMobile
                        ? "Drag to rotate • Pinch to zoom • Use preset views"
                        : "Drag to rotate • Scroll to zoom • Use preset views"
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* Product Info Below 3D Viewer */}
              <div className="product-info-section">
                <h3>{product?.name || `Custom ${productType.charAt(0).toUpperCase() + productType.slice(1)}`}</h3>
                <p className="product-description">
                  Configure your perfect {productType} with our advanced 3D customization tool.
                  Adjust dimensions, choose materials, and see your changes in real-time.
                </p>
                <div className="product-features">
                  <div className="feature-item">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                      <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="2"/>
                    </svg>
                    <span>Real-time 3D visualization</span>
                  </div>
                  <div className="feature-item">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                      <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="2"/>
                    </svg>
                    <span>Custom dimensions</span>
                  </div>
                  <div className="feature-item">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                      <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="2"/>
                    </svg>
                    <span>Premium materials</span>
                  </div>
                  <div className="feature-item">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                      <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="2"/>
                    </svg>
                    <span>Professional quality</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Configuration Panel */}
            <div className="config-panel">
              {/* Dimensions */}
              <div className={`config-card ${productType === 'chair' && isModelLoaded ? 'dimensions-active' : ''}`}>
                <div className="card-header">
                  <div className="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M3 17h18M3 7h18M7 3v18M17 3v18" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4" fill="#F0B21B"/>
                    </svg>
                  </div>
                  <div className="card-title">
                    <h4>Dimensions</h4>
                    <p>
                      {productType === 'chair' && isModelLoaded
                        ? 'Real-time 3D model scaling - see changes instantly!'
                        : 'Adjust size to fit your space'
                      }
                    </p>
                    {productType === 'chair' && isModelLoaded && (
                      <div className="real-time-indicator">
                        <div className="indicator-dot"></div>
                        <span>Live 3D Preview Active</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="dimension-controls-enhanced">
                  <div className="dimension-group-enhanced">
                    <div className="dimension-header">
                      <label className="dimension-label">Width</label>
                      <div className="dimension-value-controls">
                        <button
                          className="dimension-btn"
                          onClick={() => setDimensions({...dimensions, width: Math.max(productType === 'chair' ? 50 : 100, dimensions.width - 10)})}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M5 12h14" stroke="currentColor" strokeWidth="2"/>
                          </svg>
                        </button>
                        <div className="dimension-display">
                          <span className="dimension-value">{dimensions.width}</span>
                          <span className="dimension-unit">cm</span>
                        </div>
                        <button
                          className="dimension-btn"
                          onClick={() => setDimensions({...dimensions, width: Math.min(productType === 'chair' ? 80 : 400, dimensions.width + 10)})}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <input
                      type="range"
                      min={productType === 'chair' ? 50 : 100}
                      max={productType === 'chair' ? 80 : 400}
                      value={dimensions.width}
                      onChange={(e) => setDimensions({...dimensions, width: parseInt(e.target.value)})}
                      className="dimension-slider-enhanced"
                    />
                  </div>

                  <div className="dimension-group-enhanced">
                    <div className="dimension-header">
                      <label className="dimension-label">Depth</label>
                      <div className="dimension-value-controls">
                        <button
                          className="dimension-btn"
                          onClick={() => setDimensions({...dimensions, depth: Math.max(productType === 'chair' ? 50 : 60, dimensions.depth - 10)})}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M5 12h14" stroke="currentColor" strokeWidth="2"/>
                          </svg>
                        </button>
                        <div className="dimension-display">
                          <span className="dimension-value">{dimensions.depth}</span>
                          <span className="dimension-unit">cm</span>
                        </div>
                        <button
                          className="dimension-btn"
                          onClick={() => setDimensions({...dimensions, depth: Math.min(productType === 'chair' ? 80 : 200, dimensions.depth + 10)})}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <input
                      type="range"
                      min={productType === 'chair' ? 50 : 60}
                      max={productType === 'chair' ? 80 : 200}
                      value={dimensions.depth}
                      onChange={(e) => setDimensions({...dimensions, depth: parseInt(e.target.value)})}
                      className="dimension-slider-enhanced"
                    />
                  </div>

                  <div className="dimension-group-enhanced">
                    <div className="dimension-header">
                      <label className="dimension-label">Height</label>
                      <div className="dimension-value-controls">
                        <button
                          className="dimension-btn"
                          onClick={() => setDimensions({...dimensions, height: Math.max(productType === 'chair' ? 70 : 60, dimensions.height - 10)})}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M5 12h14" stroke="currentColor" strokeWidth="2"/>
                          </svg>
                        </button>
                        <div className="dimension-display">
                          <span className="dimension-value">{dimensions.height}</span>
                          <span className="dimension-unit">cm</span>
                        </div>
                        <button
                          className="dimension-btn"
                          onClick={() => setDimensions({...dimensions, height: Math.min(productType === 'chair' ? 120 : 120, dimensions.height + 10)})}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <input
                      type="range"
                      min={productType === 'chair' ? 70 : 60}
                      max={productType === 'chair' ? 120 : 120}
                      value={dimensions.height}
                      onChange={(e) => setDimensions({...dimensions, height: parseInt(e.target.value)})}
                      className="dimension-slider-enhanced"
                    />
                  </div>
                </div>
              </div>

              {/* Colors and Materials */}
              <div className="config-card">
                <div className="card-header">
                  <div className="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="#F0B21B" strokeWidth="2"/>
                      <path d="M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z" fill="#F0B21B"/>
                    </svg>
                  </div>
                  <div className="card-title">
                    <h4>Colors & Materials</h4>
                    <p>{isModelLoaded ? 'GLB model materials are preserved' : 'Choose colors and surface materials'}</p>
                  </div>
                </div>

                {modelError && (
                  <div className="model-error-notice">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="#ffc107" strokeWidth="2"/>
                      <path d="M12 8v4M12 16h.01" stroke="#ffc107" strokeWidth="2"/>
                    </svg>
                    {modelError}
                  </div>
                )}
                <div className="color-material-grid-enhanced">
                  <div className="color-section-enhanced">
                    <div className="section-header">
                      <h5>Primary Color</h5>
                      <div className="color-info">
                        <span className="current-color-name">
                          {colors.primary === '#6B7280' ? 'Slate Gray' :
                           colors.primary === '#374151' ? 'Dark Gray' :
                           colors.primary === '#1F2937' ? 'Charcoal' :
                           colors.primary === '#111827' ? 'Black' :
                           colors.primary === '#F3F4F6' ? 'Light Gray' :
                           colors.primary === '#E5E7EB' ? 'Silver' :
                           colors.primary === '#F0B21B' ? 'Golden Yellow' :
                           colors.primary === '#DC2626' ? 'Red' :
                           colors.primary === '#059669' ? 'Green' :
                           colors.primary === '#2563EB' ? 'Blue' :
                           colors.primary === '#7C3AED' ? 'Purple' :
                           colors.primary === '#EA580C' ? 'Orange' : 'Custom'}
                        </span>
                        <div
                          className="current-color-preview"
                          style={{ backgroundColor: colors.primary }}
                        ></div>
                      </div>
                    </div>
                    <div className="color-palette-enhanced">
                      <div className="color-group">
                        <span className="color-group-label">Neutrals</span>
                        <div className="color-row">
                          {['#6B7280', '#374151', '#1F2937', '#111827', '#F3F4F6', '#E5E7EB'].map((color) => (
                            <button
                              key={color}
                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => setColors({...colors, primary: color})}
                              title={color === '#6B7280' ? 'Slate Gray' :
                                     color === '#374151' ? 'Dark Gray' :
                                     color === '#1F2937' ? 'Charcoal' :
                                     color === '#111827' ? 'Black' :
                                     color === '#F3F4F6' ? 'Light Gray' : 'Silver'}
                            />
                          ))}
                        </div>
                      </div>
                      <div className="color-group">
                        <span className="color-group-label">Accent Colors</span>
                        <div className="color-row">
                          {['#F0B21B', '#DC2626', '#059669', '#2563EB', '#7C3AED', '#EA580C'].map((color) => (
                            <button
                              key={color}
                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}
                              style={{ backgroundColor: color }}
                              onClick={() => setColors({...colors, primary: color})}
                              title={color === '#F0B21B' ? 'Golden Yellow' :
                                     color === '#DC2626' ? 'Red' :
                                     color === '#059669' ? 'Green' :
                                     color === '#2563EB' ? 'Blue' :
                                     color === '#7C3AED' ? 'Purple' : 'Orange'}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="material-section-enhanced">
                    <div className="section-header">
                      <h5>Material & Finish</h5>
                      <span className="current-material-name">
                        {material.charAt(0).toUpperCase() + material.slice(1)}
                      </span>
                    </div>
                    <div className="material-options-enhanced">
                      {(productType === 'chair' ? [
                        { name: 'Mesh', value: 'mesh', icon: '🕸️', desc: 'Breathable mesh fabric' },
                        { name: 'Fabric', value: 'fabric', icon: '🧵', desc: 'Soft upholstery fabric' },
                        { name: 'Leather', value: 'leather', icon: '🐄', desc: 'Premium leather finish' }
                      ] : [
                        { name: 'Wood', value: 'wood', icon: '🌳', desc: 'Natural wood grain' },
                        { name: 'Metal', value: 'metal', icon: '⚙️', desc: 'Brushed metal finish' },
                        { name: 'Glass', value: 'glass', icon: '💎', desc: 'Tempered glass surface' }
                      ]).map((mat) => (
                        <button
                          key={mat.value}
                          className={`material-option-enhanced ${material === mat.value ? 'active' : ''}`}
                          onClick={() => setMaterial(mat.value)}
                          title={mat.desc}
                        >
                          <div className="material-icon">{mat.icon}</div>
                          <div className="material-info">
                            <span className="material-name">{mat.name}</span>
                            <span className="material-desc">{mat.desc}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Modern Order Summary */}
              <div className="config-card pricing-card-modern">
                <div className="card-header-modern">
                  <div className="card-icon-modern">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2M6.4 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm11.2 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="card-title-modern">
                    <h4>Order Summary</h4>
                    <p>Configure and add to cart</p>
                  </div>
                </div>

                <div className="order-content-modern">
                  {/* Product Summary */}
                  <div className="product-summary-modern">
                    <div className="product-info-row">
                      <div className="product-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <rect x="3" y="4" width="18" height="12" rx="2" stroke="#F0B21B" strokeWidth="1.5" fill="none"/>
                          <path d="M7 8h10M7 12h6" stroke="#F0B21B" strokeWidth="1.5" strokeLinecap="round"/>
                        </svg>
                      </div>
                      <div className="product-details">
                        <span className="product-name">Custom {productType.charAt(0).toUpperCase() + productType.slice(1)}</span>
                        <span className="product-specs">{dimensions.width}×{dimensions.depth}×{dimensions.height}cm</span>
                      </div>
                    </div>
                  </div>

                  {/* Quantity Controls */}
                  <div className="quantity-section-modern">
                    <div className="section-label">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                        <path d="M9 12l2 2 4-4" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="12" r="9" stroke="#F0B21B" strokeWidth="1.5"/>
                      </svg>
                      <span>Quantity</span>
                    </div>
                    <div className="quantity-controls-modern">
                      <button
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        className="quantity-btn-modern"
                        disabled={quantity <= 1}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M5 12h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        </svg>
                      </button>
                      <div className="quantity-display-modern">
                        <span className="quantity-number-modern">{quantity}</span>
                      </div>
                      <button
                        onClick={() => setQuantity(quantity + 1)}
                        className="quantity-btn-modern"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Price Breakdown */}
                  <div className="price-breakdown-modern">
                    <div className="price-row-modern">
                      <div className="price-label-modern">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="10" stroke="#64748b" strokeWidth="1.5"/>
                          <path d="M12 6v6l4 2" stroke="#64748b" strokeWidth="1.5" strokeLinecap="round"/>
                        </svg>
                        <span>Base Price</span>
                      </div>
                      <span className="price-amount">${getBasePrice().toFixed(2)}</span>
                    </div>
                    <div className="price-row-modern">
                      <div className="price-label-modern">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" stroke="#64748b" strokeWidth="1.5"/>
                          <polyline points="3.27,6.96 12,12.01 20.73,6.96" stroke="#64748b" strokeWidth="1.5"/>
                          <line x1="12" y1="22.08" x2="12" y2="12" stroke="#64748b" strokeWidth="1.5"/>
                        </svg>
                        <span>Material & Size</span>
                      </div>
                      <span className="price-amount">+${(getCurrentPrice() / quantity - getBasePrice()).toFixed(2)}</span>
                    </div>
                    <div className="price-divider"></div>
                    <div className="price-row-modern total-row">
                      <div className="price-label-modern total-label">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                          <path d="M12 2L2 7l10 5 10-5-10-5z" fill="#F0B21B"/>
                          <path d="M2 17l10 5 10-5" stroke="#F0B21B" strokeWidth="2"/>
                          <path d="M2 12l10 5 10-5" stroke="#F0B21B" strokeWidth="2"/>
                        </svg>
                        <span>Total ({quantity} item{quantity > 1 ? 's' : ''})</span>
                      </div>
                      <span className="price-amount-total">${getCurrentPrice().toFixed(2)}</span>
                    </div>
                  </div>

                  {/* Add to Cart Button */}
                  <button className="add-to-cart-btn-modern" onClick={handleAddToCart}>
                    <div className="btn-content-modern">
                      <div className="btn-icon-modern">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="9" cy="20" r="1" stroke="currentColor" strokeWidth="2"/>
                        <circle cx="20" cy="20" r="1" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                      </div>
                      <div className="btn-text-modern">
                        <span className="btn-action-modern">Add to Cart</span>
                        <span className="btn-price-modern">${getCurrentPrice().toFixed(2)}</span>
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Advanced3DConfigurator;
