import React from 'react';
import ProductCard from '../product/ProductCard';

const ProductCardDemo = () => {
    // Sample product data for demonstration
    const sampleProduct = {
        id: 1,
        name: "Executive Office Desk",
        description: "Premium executive desk with modern design and ample storage space",
        price: 899.99,
        discountPrice: null,
        categoryId: 1,
        categoryName: "Desks",
        images: [
            "/images/products/executive-desk-1.jpg",
            "/images/products/executive-desk-2.jpg"
        ],
        model3D: "/models/executive-desk.glb",
        specifications: {
            dimensions: "180cm x 90cm x 75cm",
            material: "Engineered Wood",
            weight: "45kg",
            color: "Natural Wood"
        },
        inStock: true,
        featured: true
    };

    return (
        <div style={{ 
            padding: '2rem', 
            backgroundColor: '#f8f9fa',
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '2rem'
        }}>
            <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                <h1 style={{ color: '#333', marginBottom: '1rem' }}>
                    Enhanced Product Card with 3D Configuration
                </h1>
                <p style={{ color: '#666', fontSize: '1.1rem', maxWidth: '600px' }}>
                    Interactive product card featuring quick 3D customization options. 
                    Click the configuration buttons to see real-time price updates and visual feedback.
                </p>
            </div>

            <div style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
                gap: '2rem',
                maxWidth: '1200px',
                width: '100%'
            }}>
                <ProductCard product={sampleProduct} />
                
                {/* Additional sample products for comparison */}
                <ProductCard product={{
                    ...sampleProduct,
                    id: 2,
                    name: "Ergonomic Office Chair",
                    categoryName: "Chairs",
                    price: 599.99,
                    discountPrice: 499.99,
                    images: ["/images/products/office-chair-1.jpg"]
                }} />
                
                <ProductCard product={{
                    ...sampleProduct,
                    id: 3,
                    name: "Modern Bookshelf",
                    categoryName: "Storage",
                    price: 299.99,
                    featured: false,
                    images: ["/images/products/bookshelf-1.jpg"]
                }} />
            </div>

            <div style={{ 
                backgroundColor: 'white',
                padding: '2rem',
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                maxWidth: '800px',
                width: '100%'
            }}>
                <h2 style={{ color: '#333', marginBottom: '1rem' }}>Features</h2>
                <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '1.5rem'
                }}>
                    <div>
                        <h3 style={{ color: '#F0B21B', fontSize: '1.1rem', marginBottom: '0.5rem' }}>
                            🎨 Quick Color Change
                        </h3>
                        <p style={{ color: '#666', fontSize: '0.9rem' }}>
                            Cycle through available colors with instant price updates
                        </p>
                    </div>
                    
                    <div>
                        <h3 style={{ color: '#4ECDC4', fontSize: '1.1rem', marginBottom: '0.5rem' }}>
                            🏗️ Material Selection
                        </h3>
                        <p style={{ color: '#666', fontSize: '0.9rem' }}>
                            Choose from wood, metal, glass, and premium materials
                        </p>
                    </div>
                    
                    <div>
                        <h3 style={{ color: '#45B7D1', fontSize: '1.1rem', marginBottom: '0.5rem' }}>
                            📏 Size Options
                        </h3>
                        <p style={{ color: '#666', fontSize: '0.9rem' }}>
                            Standard, compact, large, and XL sizing options
                        </p>
                    </div>
                    
                    <div>
                        <h3 style={{ color: '#F0B21B', fontSize: '1.1rem', marginBottom: '0.5rem' }}>
                            🎯 Full 3D Config
                        </h3>
                        <p style={{ color: '#666', fontSize: '0.9rem' }}>
                            Access complete 3D configurator with advanced options
                        </p>
                    </div>
                </div>
            </div>

            <div style={{ 
                backgroundColor: '#333',
                color: 'white',
                padding: '1.5rem',
                borderRadius: '8px',
                textAlign: 'center',
                maxWidth: '600px',
                width: '100%'
            }}>
                <h3 style={{ marginBottom: '0.5rem' }}>Interactive Elements</h3>
                <p style={{ margin: 0, opacity: 0.8 }}>
                    • Hover effects with smooth animations<br/>
                    • Real-time price calculation<br/>
                    • Visual feedback for active configurations<br/>
                    • Responsive design for all devices
                </p>
            </div>
        </div>
    );
};

export default ProductCardDemo;
