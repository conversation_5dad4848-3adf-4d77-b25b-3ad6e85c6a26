import React, { useState, useEffect } from 'react';
import { getCategories } from '../../services/products';

const ProductFilter = ({ filters, onFilterChange, onClearFilters }) => {
    const [categories, setCategories] = useState([]);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        loadCategories();
    }, []);

    const loadCategories = async () => {
        try {
            const response = await getCategories();
            setCategories(response.categories || []);
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    };

    const handleFilterChange = (filterType, value) => {
        onFilterChange(filterType, value);
    };

    const handlePriceChange = (type, value) => {
        const numValue = value === '' ? '' : parseFloat(value);
        onFilterChange(type, numValue);
    };

    const toggleFilter = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className="product-filter">
            <div className="filter-header">
                <h3>Filters</h3>
                <button 
                    className="mobile-filter-toggle"
                    onClick={toggleFilter}
                >
                    {isOpen ? 'Hide Filters' : 'Show Filters'}
                </button>
            </div>

            <div className={`filter-content ${isOpen ? 'open' : ''}`}>
                {/* Search Filter */}
                <div className="filter-section">
                    <label>Search</label>
                    <input
                        type="text"
                        placeholder="Search products..."
                        value={filters.search || ''}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                    />
                </div>

                {/* Category Filter */}
                <div className="filter-section">
                    <label>Category</label>
                    <select
                        value={filters.category || ''}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                    >
                        <option value="">All Categories</option>
                        {categories.map(category => (
                            <option key={category.id} value={category.name}>
                                {category.name} ({category.productCount})
                            </option>
                        ))}
                    </select>
                </div>

                {/* Price Range Filter */}
                <div className="filter-section">
                    <label>Price Range</label>
                    <div className="price-inputs">
                        <input
                            type="number"
                            placeholder="Min"
                            value={filters.minPrice || ''}
                            onChange={(e) => handlePriceChange('minPrice', e.target.value)}
                            min="0"
                            step="0.01"
                        />
                        <span>to</span>
                        <input
                            type="number"
                            placeholder="Max"
                            value={filters.maxPrice || ''}
                            onChange={(e) => handlePriceChange('maxPrice', e.target.value)}
                            min="0"
                            step="0.01"
                        />
                    </div>
                </div>

                {/* Featured Filter */}
                <div className="filter-section">
                    <label className="checkbox-label">
                        <input
                            type="checkbox"
                            checked={filters.featured || false}
                            onChange={(e) => handleFilterChange('featured', e.target.checked)}
                        />
                        Featured Products Only
                    </label>
                </div>

                {/* Sort Options */}
                <div className="filter-section">
                    <label>Sort By</label>
                    <select
                        value={filters.sortBy || 'createdAt'}
                        onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    >
                        <option value="createdAt">Newest First</option>
                        <option value="name">Name A-Z</option>
                        <option value="price">Price Low to High</option>
                        <option value="price-desc">Price High to Low</option>
                    </select>
                </div>

                {/* Sort Order */}
                <div className="filter-section">
                    <label>Order</label>
                    <select
                        value={filters.sortOrder || 'DESC'}
                        onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                    >
                        <option value="ASC">Ascending</option>
                        <option value="DESC">Descending</option>
                    </select>
                </div>

                {/* Clear Filters */}
                <div className="filter-actions">
                    <button 
                        className="btn btn-secondary"
                        onClick={onClearFilters}
                    >
                        Clear All Filters
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ProductFilter;
